<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>هناكل إيه بكرة؟</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Inter Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow-x: hidden; /* Prevent horizontal scrolling */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        /* Basic styling for page visibility and transitions */
        .page {
            display: none;
            width: 100%;
            min-height: 100vh; /* Use min-height for full viewport height */
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 1rem;
            box-sizing: border-box;
            background-color: #f8f8f8;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }
        .page.active {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }
        .scrollable-content {
            width: 100%;
            max-width: 48rem; /* Max width for content */
            overflow-y: auto;
            flex-grow: 1; /* Allow content to grow */
            padding-bottom: 2rem; /* Space at bottom */
        }
        /* Custom scrollbar for better aesthetics */
        .scrollable-content::-webkit-scrollbar {
            width: 8px;
        }
        .scrollable-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .scrollable-content::-webkit-scrollbar-thumb {
            background: #a0aec0; /* Tailwind gray-400 */
            border-radius: 10px;
        }
        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: #718096; /* Tailwind gray-500 */
        }

        /* Modal specific styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
        }
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateY(20px);
            opacity: 0;
            transition: transform 0.3s ease-out, opacity 0.3s ease-out;
            position: relative;
        }
        .modal-overlay.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        .close-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            color: #4a5568; /* Tailwind gray-700 */
        }
        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: #3b82f6; /* Tailwind blue-500 */
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .ai-response-area {
            white-space: pre-wrap; /* Preserves whitespace and newlines */
            word-wrap: break-word; /* Breaks long words */
            text-align: right; /* Ensure right-to-left alignment */
        }

        /* Enhanced Back Button Styles */
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            transition: all 0.3s ease;
            z-index: 1500;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 30px rgba(59, 130, 246, 0.6);
            background: linear-gradient(135deg, #2563eb, #1e40af);
        }

        .back-button svg {
            width: 20px;
            height: 20px;
            transition: transform 0.3s ease;
        }

        .back-button:hover svg {
            transform: translateX(3px);
        }

        /* Hide default back buttons */
        .default-back-button {
            display: none;
        }

        /* Adjust page content to account for fixed header */
        .page-content {
            padding-top: 80px;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 text-right">

    <!-- Home Page -->
    <div id="homePage" class="page active">
        <h1 class="text-5xl md:text-6xl font-extrabold text-gray-900 text-center mb-16 mt-24 leading-tight">
            <span class="block text-green-600">هناكل إيه بكرة؟</span>
            <span class="block text-lg mt-4 text-gray-600">تطبيقك لاختيار وجبتك المثالية</span>
        </h1>
        <div class="flex flex-col items-center justify-center w-full max-w-lg space-y-8">
            <button id="dietButton" class="w-full py-5 px-8 rounded-full bg-gradient-to-r from-green-500 to-green-700 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>🥗</span>
                <span>هناكل دايت</span>
            </button>
            <button id="normalButton" class="w-full py-5 px-8 rounded-full bg-gradient-to-r from-amber-400 to-amber-600 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>🍝</span>
                <span>هناكل عادي</span>
            </button>
            <button id="aboutAppButton" class="w-full py-5 px-8 rounded-full bg-indigo-500 text-white text-3xl font-bold shadow-xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <span>ℹ️</span>
                <span>عن التطبيق</span>
            </button>
        </div>
    </div>

    <!-- Category Page -->
    <div id="categoryPage" class="page">
        <button id="backToHomeButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الرئيسية</span>
        </button>
        <h2 id="categoryTitle" class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20"></h2>

        <div class="scrollable-content">
            <div id="breakfastSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>فطار</span>
                    <span>☀️</span>
                </h3>
                <button data-meal-category="breakfast" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="breakfastSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedBreakfastMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="breakfast" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <div id="lunchSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>غداء</span>
                    <span>🍜</span>
                </h3>
                <button data-meal-category="lunch" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="lunchSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedLunchMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="lunch" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <div id="dinnerSection" class="bg-white rounded-2xl p-6 mb-8 shadow-lg">
                <h3 class="text-3xl font-bold text-gray-700 mb-6 text-right flex items-center justify-end space-x-3 rtl:space-x-reverse">
                    <span>عشاء</span>
                    <span>🌙</span>
                </h3>
                <button data-meal-category="dinner" class="suggest-meal-button bg-blue-500 text-white py-4 px-8 rounded-full text-xl font-semibold block mx-auto mb-6 shadow-md hover:bg-blue-600 transition-colors transform hover:scale-105">
                    اقتراح وجبة عشوائية 🎲
                </button>
                <!-- Suggestion Note -->
                <p id="dinnerSuggestionNote" class="suggestion-note text-center text-gray-500 text-sm mt-3 animate-pulse">
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة.
                </p>
                <div id="suggestedDinnerMeal" class="suggested-meal-card hidden bg-blue-50 border border-blue-200 rounded-xl p-5 mt-4 flex-col sm:flex-row items-center justify-between shadow-inner">
                    <p class="meal-name text-2xl font-bold text-blue-800 flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    <button data-meal-category="dinner" class="view-details-button bg-blue-600 text-white px-6 py-3 rounded-full text-lg font-medium hover:bg-blue-700 transition-colors whitespace-nowrap shadow-md">
                        عرض التفاصيل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Detail Page -->
    <div id="mealDetailPage" class="page">
        <button id="backToCategoryButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الوجبات</span>
        </button>
        <h2 id="mealDetailTitle" class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20"></h2>

        <div class="scrollable-content">
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>التفاصيل</span>
                    <span>📝</span>
                </h3>
                <p id="mealDetails" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>المكونات</span>
                    <span>🍎</span>
                </h3>
                <ul id="mealIngredients" class="list-none p-0 m-0"></ul>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>طريقة التحضير</span>
                    <span>👨‍🍳</span>
                </h3>
                <p id="mealInstructions" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>السعرات الحرارية</span>
                    <span>🔥</span>
                </h3>
                <p id="mealCalories" class="text-lg text-gray-600 leading-relaxed text-right"></p>
            </div>

            <!-- AI Feature Section -->
            <div class="bg-white rounded-2xl p-6 mb-6 shadow-lg">
                <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                    <span>تعديل الوصفة بالذكاء الاصطناعي</span>
                    <span>✨</span>
                </h3>
                <button id="aiModifyRecipeButton" class="w-full bg-purple-600 text-white py-3 px-6 rounded-full text-xl font-semibold shadow-md hover:bg-purple-700 transition-colors transform hover:scale-105">
                    ✨ تعديل الوصفة بالذكاء الاصطناعي ✨
                </button>
            </div>
        </div>
    </div>

    <!-- About App Page -->
    <div id="aboutAppPage" class="page">
        <button id="backFromAboutButton" class="back-button">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span>الرئيسية</span>
        </button>
        <h2 class="text-4xl font-bold text-gray-800 text-center mb-8 mt-20">عن تطبيق "هناكل إيه بكرة؟"</h2>

        <div class="scrollable-content bg-white rounded-2xl p-6 shadow-lg">
            <p class="text-lg text-gray-700 leading-relaxed mb-4 text-right">
                تطبيق "هناكل إيه بكرة؟" هو رفيقك اليومي لاختيار وجباتك بمنتهى السهولة والمرونة! سواء كنت تتبع حمية غذائية صارمة (دايت) أو تفضل الوجبات العادية، يوفر لك التطبيق مجموعة واسعة من الاقتراحات اللذيذة والمغذية.
            </p>
            <h3 class="text-2xl font-bold text-gray-700 mb-3 text-right flex items-center justify-end space-x-2 rtl:space-x-reverse">
                <span>الميزات الرئيسية</span>
                <span>🌟</span>
            </h3>
            <ul class="list-none p-0 m-0 mb-4">
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">اقتراحات وجبات متنوعة:</span> اختر بين وجبات "دايت" أو "عادية" لفطارك، غدائك، وعشائك، مع أكثر من 200 وجبة مختلفة لكل خيار.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تفاصيل وصفات كاملة:</span> لكل وجبة، ستجد تفاصيلها، مكوناتها الدقيقة، طريقة التحضير خطوة بخطوة، وحساب تقديري للسعرات الحرارية لكل 100 جرام وحجم الوجبة.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تعديل الوصفات بالذكاء الاصطناعي:</span> هذه الميزة الثورية تسمح لك بتخصيص أي وصفة باستخدام **الذكاء الاصطناعي**! فقط اطلب من الذكاء الاصطناعي تعديل الوصفة (مثال: "اجعلها نباتية"، "أضف المزيد من البروتين"، "قلل السعرات الحرارية") وسيقوم بإعادة صياغة الوصفة لك.</li>
                <li class="text-lg text-gray-600 leading-relaxed mb-2 text-right">• <span class="font-semibold">تصميم احترافي وسهل الاستخدام:</span> واجهة جذابة، أيقونات واضحة، وتجربة مستخدم سلسة تجعل تصفح الوجبات أمراً ممتعاً.</li>
            </ul>
            <p class="text-lg text-gray-700 leading-relaxed text-right">
                نتمنى لك تجربة ممتعة ومفيدة مع تطبيق "هناكل إيه بكرة؟".
            </p>
        </div>
    </div>


    <!-- AI Modification Modal -->
    <div id="aiModal" class="modal-overlay hidden">
        <div class="modal-content">
            <span class="close-button" id="closeModalButton">&times;</span>
            <h3 class="text-2xl font-bold text-gray-700 mb-4 text-right">عدّل وصفتك بالذكاء الاصطناعي</h3>
            <textarea id="aiPromptInput" class="w-full p-3 border border-gray-300 rounded-lg mb-4 text-right" rows="4" placeholder="مثال: اجعلها نباتية، أضف المزيد من البروتين، قلل السعرات الحرارية، اجعلها خالية من الجلوتين..."></textarea>
            <button id="sendPromptButton" class="w-full bg-blue-500 text-white py-3 px-6 rounded-full text-lg font-semibold shadow-md hover:bg-blue-600 transition-colors mb-4">
                أرسل الطلب
            </button>
            <div id="aiLoadingIndicator" class="flex justify-center items-center py-4 hidden">
                <div class="loading-spinner"></div>
                <span class="mr-3 text-gray-700">الذكاء الاصطناعي يفكر...</span>
            </div>
            <div id="aiResponseArea" class="bg-gray-50 p-4 rounded-lg border border-gray-200 text-gray-800 text-lg ai-response-area min-h-[100px] max-h-[300px] overflow-y-auto">
                <!-- AI response will be displayed here -->
            </div>
             <p id="aiErrorDisplay" class="text-red-600 text-sm mt-2 hidden text-right"></p>
        </div>
    </div>

    <script>
        // --- Lists of specific meal names for better variety ---
        const dietBreakfastNames = [
            "شوفان بالتوت واللوز", "بيض مسلوق مع سبانخ وطماطم", "زبادي يوناني مع الفاكهة والعسل", "ساندويتش جبنة قريش بالخيار",
            "سموثي أخضر بالكيوي والسبانخ", "توست أفوكادو بالبيض المسلوق", "فطائر الشوفان الصحية بالتفاح", "جرانولا منزلية بالفواكه المجففة",
            "سموثي البروتين بالموز وزبدة الفول السوداني", "فول مدمس خفيف بزيت الزيتون والكمون", "بان كيك الشوفان", "أومليت الخضروات"
        ];
        const dietLunchNames = [
            "سلطة دجاج مشوي بالخضروات الورقية", "سمك سلمون مشوي مع أرز بسمتي بني", "شوربة عدس بالخضروات المشكلة", "خضار سوتيه وصدر دجاج بالليمون",
            "سلطة الكينوا بالخضروات والحمص", "تونا بالخضروات الطازجة وصوص الزبادي", "رول الخس بالدجاج والخضار", "عدس بجبنة البارميزان الخفيفة",
            "بوريتو بول صحي بالدجاج والأرز البري", "خضروات مشوية وحمص بالليمون والثوم", "شوربة الطماطم بالريحان", "فيليه دجاج مشوي"
        ];
        const dietDinnerNames = [
            "جبنة قريش بالخضروات المقطعة وزيت الزيتون", "سلطة تونة لايت بالذرة والفلفل", "صدر دجاج مسلوق مع بروكلي على البخار", "شوربة بروكلي بالكريمة الخفيفة",
            "بيض أومليت خفيف بالسبانخ والفطر", "زبادي وخيار بالنعناع", "خضروات مشوية خفيفة بالبهارات", "سمك فيليه على البخار مع الليمون",
            "سلطة فواكه بالزبادي قليل الدسم", "دجاج مسلوق وخضروات مسلوقة", "سلطة يونانية خفيفة", "شوربة الخضار بالكريمة"
        ];

        const normalBreakfastNames = [
            "فول بالزيت والليمون مع البيض المدحرج", "بيض بالبسطرمة والجبنة الموتزاريلا", "مناقيش جبنة وزعتر طازجة من الفرن", "شكشوكة بالبيض والطماطم والفلفل",
            "ساندويتشات الجبن الساخن بالزبدة", "فطائر باللحمة المفرومة والبصل", "خبز بالبيض والجبن الشيدر", "عجة البيض بالخضروات والبهارات",
            "حواوشي فطار باللحم المفروم والخبز البلدي", "بيض بالنقانق والبطاطس المقلية", "طعمية سخنة", "مربى وقشطة وعسل"
        ];
        const normalLunchNames = [
            "مكرونة بالبشاميل واللحم المفروم الغني", "محاشي مشكلة (كوسة، باذنجان، فلفل) بالأرز والخلطة السرية", "أرز بالخضار وقطع الدجاج المتبلة", "كشري مصري بجميع مكوناته",
            "فتة شاورما الدجاج بالثومية والعيش المقلي", "كفتة بالطحينة وصوص الطماطم", "طواجن لحمة بالبطاطس في الفرن", "مبكبكة باللحم المتبل والمكرونة",
            "أرز معمر باللحم والزبدة", "صيادية السمك البوري بالصوص البني", "كبدة إسكندراني بالردة", "فته كوارع"
        ];
        const normalDinnerNames = [
            "حواوشي إسكندراني بالجبنة", "بطاطس محمرة وبرجر لحم بالجبنة", "كشري حلة واحدة سريع ولذيذ", "فطير مشلتت بالعسل الأسود والطحينة",
            "صينية دجاج بالبطاطس والجزر في الفرن", "شوربة سي فود بالكريمة والمأكولات البحرية", "معكرونة بالصلصة الحمراء واللحم المفروم", "بيتزا منزلية بالخضروات والجبن",
            "شيش طاووق على الفحم مع الأرز المبهر", "ساندويتشات كبدة بالعيش الفينو", "كفتة الأرز بالصلصة", "ساندويتشات التونة"
        ];

        // --- Data Generation ---
        const generateMeals = (nameList, isDiet, type) => {
            const meals = [];
            nameList.forEach((baseMealName, i) => {
                let name = baseMealName;
                let details = `وجبة ${isDiet ? 'صحية ' : 'شهية '} ولذيذة، مثالية لوجبة ${type}. يمكنك الاستمتاع بها كخيار سريع ومغذي.`;
                let ingredients = [];
                let instructions = '';
                let caloriesPer100g;
                let servingSizeGrams;

                if (type === 'breakfast') {
                    ingredients = ["مكون أساسي (حبوب/بيض)", "خضروات/فواكه طازجة", "مصدر بروتين خفيف"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (200 - 150 + 1)) + 150 : Math.floor(Math.random() * (250 - 200 + 1)) + 200;
                    servingSizeGrams = Math.floor(Math.random() * (200 - 150 + 1)) + 150;
                } else if (type === 'lunch') {
                    ingredients = ["مصدر بروتين (دجاج/لحم/سمك)", "كربوهيدرات معقدة (أرز بني/كينوا/خضار)", "خضروات طازجة متنوعة"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (250 - 200 + 1)) + 200 : Math.floor(Math.random() * (350 - 300 + 1)) + 300;
                    servingSizeGrams = Math.floor(Math.random() * (300 - 200 + 1)) + 200;
                } else if (type === 'dinner') {
                    ingredients = ["خضروات خفيفة (مطبوخة/سلطة)", "بروتين قليل الدسم (جبن/زبادي/دجاج)", "مصدر ألياف (خبز أسمر/بقوليات)"];
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (180 - 120 + 1)) + 120 : Math.floor(Math.random() * (280 - 220 + 1)) + 220;
                    servingSizeGrams = Math.floor(Math.random() * (250 - 150 + 1)) + 150;
                }

                if (baseMealName.includes('شوفان')) {
                    ingredients = ["1/2 كوب شوفان", "1 كوب ماء/حليب قليل الدسم", "1/2 كوب توت مشكل", "ملعقة صغيرة عسل/مكسرات"];
                    instructions = `لتحضير ${name}:\n1. اخلط نصف كوب شوفان مع كوب ماء أو حليب قليل الدسم في قدر.\n2. سخّن على نار متوسطة مع التحريك المستمر حتى يتكثف.\n3. أضف التوت والمكسرات (مثل اللوز) وملعقة صغيرة من العسل أو بديل السكر.\n4. قدّمها ساخنة واستمتع بوجبة فطار غنية بالألياف.`;
                    caloriesPer100g = isDiet ? 150 : 200;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('بيض مسلوق')) {
                    ingredients = ["2 بيضة مسلوقة", "1 كوب سبانخ طازجة", "1 طماطم متوسطة"];
                    instructions = `لتحضير ${name}:\n1. اسلق البيض لمدة 7-10 دقائق.\n2. قطّع السبانخ والطماطم.\n3. قشّر البيض وقطّعه.\n4. اخلط المكونات مع رشة ملح وفلفل.`;
                    caloriesPer100g = isDiet ? 155 : 180;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('زبادي')) {
                    ingredients = ["1 كوب زبادي يوناني قليل الدسم", "1/2 كوب فراولة مقطعة", "1/4 كوب كيوي مقطع", "ملعقة صغيرة بذور شيا"];
                    instructions = `لتحضير ${name}:\n1. ضع الزبادي في وعاء.\n2. أضف الفاكهة المقطعة فوق الزبادي.\n3. رش بذور الشيا.\n4. استمتع بوجبة خفيفة ومنعشة.`;
                    caloriesPer100g = isDiet ? 60 : 80;
                    servingSizeGrams = 250;
                } else if (baseMealName.includes('بان كيك الشوفان')) {
                    ingredients = ["1 كوب شوفان مطحون", "1 موزة مهروسة", "2 بيضة", "1/2 كوب حليب", "1 ملعقة صغيرة بيكنج باودر", "رشة قرفة"];
                    instructions = `لتحضير ${name}:\n1. اخلط الشوفان المطحون مع البيكنج باودر والقرفة.\n2. اهرس الموزة وأضف البيض والحليب واخفق جيداً.\n3. أضف خليط المكونات السائلة إلى الجافة.\n4. سخّن مقلاة واسكب ربع كوب من الخليط لكل بان كيك.\n5. اطهيها لمدة 2-3 دقائق على كل جانب.`;
                    caloriesPer100g = isDiet ? 180 : 220;
                    servingSizeGrams = 180;
                } else if (baseMealName.includes('أومليت الخضروات')) {
                    ingredients = ["2 بيضة", "1/4 كوب فلفل ألوان مقطع", "1/4 كوب فطر مقطع", "قليل من البصل المفروم", "ملح وفلفل", "زيت زيتون"];
                    instructions = `لتحضير ${name}:\n1. اخفق البيض مع الملح والفلفل.\n2. سخّن زيت الزيتون في مقلاة وشوّح الخضروات.\n3. اسكب البيض فوق الخضروات واتركه ينضج.`;
                    caloriesPer100g = isDiet ? 130 : 160;
                    servingSizeGrams = 160;
                } else if (baseMealName.includes('سلطة دجاج مشوي')) {
                    ingredients = ["150 جرام صدر دجاج مشوي", "2 كوب خس متنوع", "1 طماطم كبيرة", "1/2 خيار", "صلصة سلطة لايت"];
                    instructions = `لتحضير ${name}:\n1. اشوي صدر الدجاج وقطّعه.\n2. اخلط الخضروات في وعاء كبير.\n3. أضف الدجاج إلى السلطة.\n4. حضّر صلصة لايت واسكبها فوق السلطة.`;
                    caloriesPer100g = isDiet ? 120 : 150;
                    servingSizeGrams = 350;
                } else if (baseMealName.includes('سمك') && type === 'lunch') {
                    ingredients = ["150 جرام فيليه سمك", "1/2 كوب أرز بني مطبوخ", "1 كوب بروكلي مطبوخ", "شريحة ليمون"];
                    instructions = `لتحضير ${name}:\n1. اشوِ فيليه السمك في الفرن.\n2. اسلق الأرز البني والبروكلي.\n3. قدّم السمك مع الأرز والبروكلي.`;
                    caloriesPer100g = isDiet ? 140 : 170;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('شوربة الطماطم بالريحان')) {
                    ingredients = ["4 حبات طماطم كبيرة", "1 بصلة متوسطة", "2 فص ثوم", "1/4 كوب ريحان طازج", "2 كوب مرق خضروات", "ملح وفلفل"];
                    instructions = `لتحضير ${name}:\n1. شوّح البصل والثوم.\n2. أضف الطماطم ومرق الخضروات واتركها تغلي.\n3. اخلط الشوربة في الخلاط.\n4. أعدها للقدر وأضف الريحان والبهارات.`;
                    caloriesPer100g = isDiet ? 50 : 70;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('فيليه دجاج مشوي')) {
                    ingredients = ["150 جرام فيليه دجاج", "تتبيلة (ليمون، ثوم، أعشاب)", "خضروات مشوية"];
                    instructions = `لتحضير ${name}:\n1. تبّل فيليه الدجاج.\n2. اشوي الدجاج على الشواية.\n3. قدمه مع الخضروات المشوية.`;
                    caloriesPer100g = isDiet ? 165 : 190;
                    servingSizeGrams = 220;
                } else if (baseMealName.includes('مكرونة بالبشاميل')) {
                    ingredients = ["200 جرام مكرونة قلم", "250 جرام لحم مفروم", "500 مل بشاميل", "جبنة موتزاريلا"];
                    instructions = `لتحضير ${name}:\n1. اسلق المكرونة.\n2. حضّر اللحم المفروم المعصّج.\n3. حضّر صلصة البشاميل.\n4. رص المكونات في صينية وأدخلها الفرن.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 450;
                } else if (baseMealName.includes('جبنة قريش') && type === 'dinner') {
                    ingredients = ["100 جرام جبنة قريش", "1 طماطم صغيرة", "1/2 فلفل أخضر", "رشة زيت زيتون وزعتر"];
                    instructions = `لتحضير ${name}:\n1. ضع الجبنة القريش في طبق.\n2. قطّع الخضروات وأضفها فوق الجبنة.\n3. رش زيت الزيتون والزعتر.`;
                    caloriesPer100g = isDiet ? 98 : 120;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('سلطة يونانية خفيفة')) {
                    ingredients = ["1 خيار", "1 طماطم", "1/2 بصلة حمراء", "زيتون كالاماتا", "50 جرام جبنة فيتا قليلة الدسم", "صلصة (زيت زيتون، ليمون، أوريجانو)"];
                    instructions = `لتحضير ${name}:\n1. قطّع الخضروات.\n2. أضف الزيتون وجبنة الفيتا.\n3. حضّر الصلصة واسكبها فوق السلطة.`;
                    caloriesPer100g = isDiet ? 90 : 120;
                    servingSizeGrams = 280;
                } else if (baseMealName.includes('شوربة الخضار بالكريمة')) {
                    ingredients = ["1 كوب خضروات مشكلة", "1 بصلة مفرومة", "2 كوب مرق دجاج", "1/2 كوب كريمة طبخ لايت", "ملح وفلفل"];
                    instructions = `لتحضير ${name}:\n1. شوّح البصل وأضف الخضروات والمرق.\n2. اتركها تغلي حتى تنضج الخضروات.\n3. اخلط نصف الشوربة وأعدها للقدر.\n4. أضف الكريمة والبهارات.`;
                    caloriesPer100g = isDiet ? 80 : 110;
                    servingSizeGrams = 280;
                } else if (baseMealName.includes('حواوشي')) {
                    ingredients = ["1 رغيف خبز بلدي", "150 جرام لحم مفروم", "بصل مفروم", "فلفل أخضر", "بهارات حواوشي"];
                    instructions = `لتحضير ${name}:\n1. اخلط اللحم المفروم مع البصل والفلفل والبهارات.\n2. احشي الخبز بالخليط.\n3. اخبزه في الفرن حتى ينضج.`;
                    caloriesPer100g = 350;
                    servingSizeGrams = 250;
                } else if (baseMealName.includes('طعمية سخنة')) {
                    ingredients = ["1 كوب فول مدشوش منقوع", "1/2 حزمة كزبرة خضراء", "1 بصلة صغيرة", "2 فص ثوم", "كمون، ملح", "زيت للقلي"];
                    instructions = `لتحضير ${name}:\n1. افرم الفول مع الخضرة والبصل والثوم.\n2. أضف البهارات وشكّل العجينة.\n3. اقليها في زيت غزير وساخن.`;
                    caloriesPer100g = 250;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('كبدة إسكندراني بالردة')) {
                    ingredients = ["250 جرام كبدة شرائح", "1/2 كوب ردة", "فلفل أخضر حار", "ثوم مفروم", "كمون، ملح", "زيت للقلي"];
                    instructions = `لتحضير ${name}:
1. تبّل الكبدة بالثوم والبهارات.
2. غطّها بالردة واقليها في الزيت.
3. شوّح الفلفل وقدمه مع الكبدة.`;
                    caloriesPer100g = 300;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('فتة كوارع')) {
                    ingredients = ["1 زوج كوارع منظف", "2 كوب أرز مصري", "3 أرغفة خبز بلدي", "4 فصوص ثوم", "1/2 كوب خل", "صلصة طماطم", "سمن"];
                    instructions = `لتحضير ${name}:
1. اسلق الكوارع جيداً مع البهارات (ورق لورا، حبهان) حتى تنضج تماماً.
2. حمّص الخبز المقطع في السمن حتى يصبح ذهبياً.
3. حضّر الأرز الأبيض المفلفل.
4. حضّر صلصة الفتة: شوّح الثوم في السمن، أضف الخل ثم صلصة الطماطم واتركها تتسبك.
5. في طبق التقديم، ضع الخبز المحمص، اسقه بقليل من شوربة الكوارع، ثم ضع الأرز، ثم الصلصة، وأخيراً قطع الكوارع.`;
                    caloriesPer100g = 320;
                    servingSizeGrams = 500;
                } else if (baseMealName.includes('ساندويتشات التونة')) {
                    ingredients = ["1 علبة تونة مصفاة", "2 ملعقة كبيرة مايونيز", "1/4 بصلة صغيرة مفرومة", "فلفل ألوان مقطع", "خبز فينو أو توست"];
                    instructions = `لتحضير ${name}:
1. اخلط التونة مع المايونيز والبصل والفلفل.
2. أضف الملح والفلفل حسب الرغبة.
3. احشي الخبز بخليط التونة وقدمها.`;
                    caloriesPer100g = 220;
                    servingSizeGrams = 180;
                } else if (baseMealName.includes('كفتة الأرز بالصلصة')) {
                    ingredients = ["1/2 كيلو لحم مفروم", "1 كوب أرز مطحون", "خضرة (شبت، بقدونس، كزبرة)", "1 بصلة", "صلصة طماطم", "زيت للقلي"];
                    instructions = `لتحضير ${name}:
1. اخلط اللحم مع الأرز المطحون والخضرة والبصل المفروم والبهارات.
2. شكّل الخليط على هيئة أصابع.
3. اقلي الكفتة في الزيت حتى تتحمر.
4. حضّر صلصة الطماطم وضع فيها الكفتة المقلية واتركها على نار هادئة لتتسبك.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 300;
                } else {
                    instructions = `لتحضير ${name}:
1. جهّز المكونات الأساسية للوجبة.
2. اتبع طريقة الطهي المناسبة (شوي، سلق، قلي).
3. قدّم الوجبة ساخنة مع طبق جانبي من اختيارك.`;
                }

                const totalCalories = Math.round((caloriesPer100g / 100) * servingSizeGrams);
                const calorieString = `إجمالي السعرات الحرارية: ${totalCalories} سعرة حرارية (لكل 100 جرام: ${caloriesPer100g} سعرة حرارية، حجم الوجبة: ${servingSizeGrams} جرام)`;

                meals.push({ id: `${baseMealName.replace(/\s/g, '')}_${i}`, name, details, ingredients, instructions, calories: calorieString });
            });
            return meals;
        };

        const dietMeals = {
            breakfast: generateMeals(dietBreakfastNames, true, 'breakfast'),
            lunch: generateMeals(dietLunchNames, true, 'lunch'),
            dinner: generateMeals(dietDinnerNames, true, 'dinner'),
        };

        const normalMeals = {
            breakfast: generateMeals(normalBreakfastNames, false, 'breakfast'),
            lunch: generateMeals(normalLunchNames, false, 'lunch'),
            dinner: generateMeals(normalDinnerNames, false, 'dinner'),
        };
        // --- End Data Generation ---

        // All main logic and event listeners are now defined inside window.onload
        window.onload = () => {
            // Helper function to get a random meal
            const getRandomMeal = (mealType, dietType) => {
                const meals = dietType === 'diet' ? dietMeals : normalMeals;
                const categoryMeals = meals[mealType];
                const randomIndex = Math.floor(Math.random() * categoryMeals.length);
                return categoryMeals[randomIndex];
            };

            // Global state variables
            let currentDietType = '';
            let currentSuggestedMealObjects = { breakfast: null, lunch: null, dinner: null };
            let currentMealBeingViewed = null;

            // DOM Elements
            const homePage = document.getElementById('homePage');
            const categoryPage = document.getElementById('categoryPage');
            const mealDetailPage = document.getElementById('mealDetailPage');
            const aboutAppPage = document.getElementById('aboutAppPage');
            const aiModal = document.getElementById('aiModal');
            const aiPromptInput = document.getElementById('aiPromptInput');
            const sendPromptButton = document.getElementById('sendPromptButton');
            const aiLoadingIndicator = document.getElementById('aiLoadingIndicator');
            const aiResponseArea = document.getElementById('aiResponseArea');
            const aiErrorDisplay = document.getElementById('aiErrorDisplay');
            const dietButton = document.getElementById('dietButton');
            const normalButton = document.getElementById('normalButton');
            const aboutAppButton = document.getElementById('aboutAppButton');
            const backToHomeButton = document.getElementById('backToHomeButton');
            const backToCategoryButton = document.getElementById('backToCategoryButton');
            const backFromAboutButton = document.getElementById('backFromAboutButton');
            const categoryTitle = document.getElementById('categoryTitle');
            const suggestMealButtons = document.querySelectorAll('.suggest-meal-button');
            const suggestedMealCards = {
                breakfast: document.getElementById('suggestedBreakfastMeal'),
                lunch: document.getElementById('suggestedLunchMeal'),
                dinner: document.getElementById('suggestedDinnerMeal')
            };
            const viewDetailsButtons = document.querySelectorAll('.view-details-button');
            const mealDetailTitle = document.getElementById('mealDetailTitle');
            const mealDetails = document.getElementById('mealDetails');
            const mealIngredients = document.getElementById('mealIngredients');
            const mealInstructions = document.getElementById('mealInstructions');
            const mealCalories = document.getElementById('mealCalories');
            const allSuggestionNotes = {
                breakfast: document.getElementById('breakfastSuggestionNote'),
                lunch: document.getElementById('lunchSuggestionNote'),
                dinner: document.getElementById('dinnerSuggestionNote')
            };

            // Navigation Functions
            const showPage = (pageId) => {
                [homePage, categoryPage, mealDetailPage, aboutAppPage].forEach(p => p.classList.remove('active'));
                document.getElementById(pageId).classList.add('active');
                window.scrollTo(0, 0);
            };

            const navigateToCategory = (dietType) => {
                currentDietType = dietType;
                categoryTitle.textContent = dietType === 'diet' ? 'وجبات الدايت' : 'وجبات عادية';
                currentSuggestedMealObjects = { breakfast: null, lunch: null, dinner: null };
                document.querySelectorAll('.suggested-meal-card').forEach(card => {
                    card.classList.add('hidden');
                    card.style.display = 'none';
                });
                Object.values(allSuggestionNotes).forEach(note => {
                    if (note) {
                        note.classList.remove('hidden');
                        note.style.display = 'block';
                    }
                });
                showPage('categoryPage');
            };

            const navigateToMealDetail = (meal) => {
                currentMealBeingViewed = meal;
                mealDetailTitle.textContent = meal.name;
                mealDetails.textContent = meal.details;
                mealInstructions.textContent = meal.instructions;
                mealCalories.textContent = meal.calories;
                mealIngredients.innerHTML = '';
                meal.ingredients.forEach(item => {
                    const li = document.createElement('li');
                    li.className = 'text-lg text-gray-600 leading-relaxed mb-1 text-right';
                    li.textContent = `• ${item}`;
                    mealIngredients.appendChild(li);
                });
                showPage('mealDetailPage');
            };

            const toggleAiModal = (show) => {
                if (show) {
                    aiModal.classList.add('active');
                    aiModal.classList.remove('hidden');
                    aiPromptInput.value = '';
                    aiResponseArea.textContent = '';
                    aiErrorDisplay.classList.add('hidden');
                } else {
                    aiModal.classList.remove('active');
                    setTimeout(() => aiModal.classList.add('hidden'), 300);
                }
            };

            // Gemini API Call Function
            const callGeminiAPI = async (prompt) => {
                aiLoadingIndicator.classList.remove('hidden');
                aiResponseArea.textContent = '';
                aiErrorDisplay.classList.add('hidden');
                const apiKey = "";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Gemini API Error: ${response.status} - ${errorData.error.message || 'Unknown error'}`);
                    }
                    const result = await response.json();
                    if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts) {
                        aiResponseArea.textContent = result.candidates[0].content.parts[0].text;
                    } else {
                        throw new Error("Gemini API did not return expected content.");
                    }
                } catch (error) {
                    aiResponseArea.textContent = "عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.";
                    aiErrorDisplay.textContent = `خطأ: ${error.message}`;
                    aiErrorDisplay.classList.remove('hidden');
                } finally {
                    aiLoadingIndicator.classList.add('hidden');
                }
            };

            // Attach Event Listeners
            dietButton.addEventListener('click', () => navigateToCategory('diet'));
            normalButton.addEventListener('click', () => navigateToCategory('normal'));
            aboutAppButton.addEventListener('click', () => showPage('aboutAppPage'));
            backToHomeButton.addEventListener('click', () => showPage('homePage'));
            backToCategoryButton.addEventListener('click', () => navigateToCategory(currentDietType));
            backFromAboutButton.addEventListener('click', () => showPage('homePage'));

            suggestMealButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const mealCategory = event.target.dataset.mealCategory;
                    const meal = getRandomMeal(mealCategory, currentDietType);
                    currentSuggestedMealObjects[mealCategory] = meal;
                    const cardElement = suggestedMealCards[mealCategory];
                    const suggestionNoteElement = allSuggestionNotes[mealCategory];
                    if (cardElement && meal) {
                        cardElement.querySelector('.meal-name').textContent = meal.name;
                        cardElement.classList.remove('hidden');
                        cardElement.style.display = 'flex';
                        if (suggestionNoteElement) {
                            suggestionNoteElement.classList.add('hidden');
                            suggestionNoteElement.style.display = 'none';
                        }
                    }
                });
            });

            viewDetailsButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const mealCategory = event.target.dataset.mealCategory;
                    const mealToDisplay = currentSuggestedMealObjects[mealCategory];
                    if (mealToDisplay) {
                        navigateToMealDetail(mealToDisplay);
                    } else {
                        const fallbackMeal = getRandomMeal(mealCategory, currentDietType);
                        navigateToMealDetail(fallbackMeal);
                    }
                });
            });

            aiModifyRecipeButton.addEventListener('click', () => {
                if (currentMealBeingViewed) {
                    toggleAiModal(true);
                } else {
                    alert("يرجى اختيار وجبة أولاً لعرض تفاصيلها.");
                }
            });

            closeModalButton.addEventListener('click', () => toggleAiModal(false));

            sendPromptButton.addEventListener('click', () => {
                const userPrompt = aiPromptInput.value.trim();
                if (!userPrompt) {
                    aiErrorDisplay.textContent = "الرجاء إدخال طلبك.";
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }
                if (!currentMealBeingViewed) {
                    aiResponseArea.textContent = "عذراً، لا توجد وجبة حالية لتعديلها.";
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }
                const mealDetailsForAI = `الاسم: ${currentMealBeingViewed.name}\nالتفاصيل: ${currentMealBeingViewed.details}\nالمكونات: ${currentMealBeingViewed.ingredients.join(', ')}\nطريقة التحضير: ${currentMealBeingViewed.instructions}\nالسعرات الحرارية: ${currentMealBeingViewed.calories}`;
                const fullPrompt = `أرغب في تعديل وصفة الطعام التالية:\n${mealDetailsForAI}\n\nطلب التعديل: ${userPrompt}\n\nيرجى إعادة كتابة الوصفة بعد التعديل، مع ذكر المكونات وطريقة التحضير والسعرات الحرارية المعدلة (تقديرية). قدم النتيجة بتنسيق واضح وسهل القراءة.`;
                callGeminiAPI(fullPrompt);
            });

            // Initial page load
            showPage('homePage');
        };
    </script>
</body>
</html>
