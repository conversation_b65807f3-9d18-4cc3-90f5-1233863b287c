<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>هناكل إيه بكرة؟</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Cairo (Arabic) + Poppins (English) -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --shadow-soft: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            --shadow-hover: 0 15px 35px rgba(31, 38, 135, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Poppins', sans-serif;
            margin: 0;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundMove 20s ease-in-out infinite;
        }

        @keyframes backgroundMove {
            0%, 100% { transform: translateX(0) translateY(0); }
            25% { transform: translateX(-20px) translateY(-10px); }
            50% { transform: translateX(20px) translateY(10px); }
            75% { transform: translateX(-10px) translateY(20px); }
        }
        /* Glass Morphism Effect */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 20px;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-soft);
        }

        .glass:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }

        /* Page Styling with Glass Effect */
        .page {
            display: none;
            width: 100%;
            min-height: 100vh;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 2rem 1rem;
            box-sizing: border-box;
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page.active {
            display: flex;
            opacity: 1;
            transform: translateY(0) scale(1);
        }
        .scrollable-content {
            width: 100%;
            max-width: 56rem;
            overflow-y: auto;
            flex-grow: 1;
            padding-bottom: 2rem;
        }

        /* Custom Scrollbar with Glass Effect */
        .scrollable-content::-webkit-scrollbar {
            width: 12px;
        }
        .scrollable-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .scrollable-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2));
            border-radius: 10px;
            border: 2px solid transparent;
            background-clip: content-box;
        }
        .scrollable-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.3));
        }

        /* Modern Button Styles */
        .btn-modern {
            position: relative;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .btn-modern:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .btn-modern:active {
            transform: translateY(-1px) scale(1.02);
        }

        /* Enhanced Modal with Glass Effect */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 2.5rem;
            border-radius: 25px;
            width: 90%;
            max-width: 650px;
            border: 1px solid var(--glass-border);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            transform: translateY(50px) scale(0.9);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            color: white;
        }

        .modal-overlay.active .modal-content {
            transform: translateY(0) scale(1);
            opacity: 1;
        }
        .close-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.8);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: rotate(90deg);
        }

        /* Enhanced Loading Spinner */
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-left-color: #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Floating Animation */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .float-animation {
            animation: float 3s ease-in-out infinite;
        }

        .ai-response-area {
            white-space: pre-wrap;
            word-wrap: break-word;
            text-align: right;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
        }

        /* Enhanced Back Button with Glass Effect */
        .back-button {
            position: fixed;
            top: 25px;
            right: 25px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 140px;
            justify-content: center;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .back-button:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: var(--shadow-hover);
            background: rgba(255, 255, 255, 0.3);
        }

        .back-button svg {
            width: 20px;
            height: 20px;
            transition: transform 0.3s ease;
        }

        .back-button:hover svg {
            transform: translateX(5px);
        }

        /* Hide default back buttons */
        .default-back-button {
            display: none;
        }

        /* Adjust page content to account for fixed header */
        .page-content {
            padding-top: 80px;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100 text-right">

    <!-- Home Page -->
    <div id="homePage" class="page active">
        <!-- Hero Section with Glass Effect -->
        <div class="glass float-animation text-center p-12 mb-12 max-w-4xl">
            <h1 class="text-6xl md:text-7xl font-black text-white mb-6 leading-tight">
                <span class="block bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
                    هناكل إيه بكرة؟
                </span>
            </h1>
            <p class="text-xl md:text-2xl text-white/80 font-medium mb-8">
                <i class="fas fa-utensils mr-3"></i>
                تطبيقك الذكي لاختيار وجبتك المثالية
            </p>
            <div class="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/60">
                <div class="flex items-center">
                    <i class="fas fa-star text-yellow-400 mr-2"></i>
                    <span>أكثر من 200 وجبة</span>
                </div>
                <div class="w-1 h-1 bg-white/40 rounded-full"></div>
                <div class="flex items-center">
                    <i class="fas fa-robot text-blue-400 mr-2"></i>
                    <span>ذكاء اصطناعي</span>
                </div>
                <div class="w-1 h-1 bg-white/40 rounded-full"></div>
                <div class="flex items-center">
                    <i class="fas fa-heart text-red-400 mr-2"></i>
                    <span>وصفات صحية</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col items-center justify-center w-full max-w-2xl space-y-6">
            <button id="dietButton" class="btn-modern w-full py-6 px-10 text-white text-2xl font-bold flex items-center justify-center space-x-4 rtl:space-x-reverse" style="background: var(--success-gradient);">
                <i class="fas fa-leaf text-3xl"></i>
                <span>هناكل دايت</span>
                <i class="fas fa-arrow-left"></i>
            </button>
            <button id="normalButton" class="btn-modern w-full py-6 px-10 text-white text-2xl font-bold flex items-center justify-center space-x-4 rtl:space-x-reverse" style="background: var(--warning-gradient);">
                <i class="fas fa-pizza-slice text-3xl"></i>
                <span>هناكل عادي</span>
                <i class="fas fa-arrow-left"></i>
            </button>
            <button id="aboutAppButton" class="btn-modern w-full py-6 px-10 text-white text-2xl font-bold flex items-center justify-center space-x-4 rtl:space-x-reverse" style="background: var(--primary-gradient);">
                <i class="fas fa-info-circle text-3xl"></i>
                <span>عن التطبيق</span>
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>

        <!-- Features Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 max-w-6xl w-full">
            <div class="glass p-6 text-center">
                <i class="fas fa-magic text-4xl text-purple-400 mb-4"></i>
                <h3 class="text-xl font-bold text-white mb-2">ذكاء اصطناعي</h3>
                <p class="text-white/70">تعديل الوصفات بالذكاء الاصطناعي</p>
            </div>
            <div class="glass p-6 text-center">
                <i class="fas fa-random text-4xl text-green-400 mb-4"></i>
                <h3 class="text-xl font-bold text-white mb-2">اقتراحات عشوائية</h3>
                <p class="text-white/70">وجبات متنوعة في كل مرة</p>
            </div>
            <div class="glass p-6 text-center">
                <i class="fas fa-chart-line text-4xl text-blue-400 mb-4"></i>
                <h3 class="text-xl font-bold text-white mb-2">حساب السعرات</h3>
                <p class="text-white/70">معلومات غذائية دقيقة</p>
            </div>
        </div>
    </div>

    <!-- Category Page -->
    <div id="categoryPage" class="page">
        <button id="backToHomeButton" class="back-button">
            <i class="fas fa-arrow-right"></i>
            <span>الرئيسية</span>
        </button>

        <!-- Page Header -->
        <div class="glass text-center p-8 mb-12 max-w-3xl">
            <h2 id="categoryTitle" class="text-5xl font-black text-white mb-4"></h2>
            <p class="text-white/80 text-lg">
                <i class="fas fa-utensils mr-2"></i>
                اختر وجبتك المفضلة من الفئات التالية
            </p>
        </div>

        <div class="scrollable-content">
            <!-- Breakfast Section -->
            <div id="breakfastSection" class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-sun text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-3xl font-bold text-white mb-2">فطار</h3>
                            <p class="text-white/70">ابدأ يومك بوجبة مغذية</p>
                        </div>
                    </div>
                </div>

                <button data-meal-category="breakfast" class="suggest-meal-button btn-modern w-full py-4 px-8 text-white text-xl font-semibold mb-6" style="background: var(--warning-gradient);">
                    <i class="fas fa-dice mr-3"></i>
                    اقتراح وجبة عشوائية
                    <i class="fas fa-magic ml-3"></i>
                </button>

                <p id="breakfastSuggestionNote" class="suggestion-note text-center text-white/60 text-sm mt-3 animate-pulse">
                    <i class="fas fa-info-circle mr-2"></i>
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة
                </p>

                <div id="suggestedBreakfastMeal" class="suggested-meal-card hidden glass p-6 mt-6 flex-col sm:flex-row items-center justify-between">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse flex-grow">
                        <i class="fas fa-utensils text-2xl text-yellow-400"></i>
                        <p class="meal-name text-2xl font-bold text-white flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    </div>
                    <button data-meal-category="breakfast" class="view-details-button btn-modern px-6 py-3 text-white font-medium whitespace-nowrap" style="background: var(--primary-gradient);">
                        <i class="fas fa-eye mr-2"></i>
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <!-- Lunch Section -->
            <div id="lunchSection" class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-16 h-16 bg-gradient-to-r from-red-400 to-pink-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-utensils text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-3xl font-bold text-white mb-2">غداء</h3>
                            <p class="text-white/70">وجبة رئيسية مشبعة ولذيذة</p>
                        </div>
                    </div>
                </div>

                <button data-meal-category="lunch" class="suggest-meal-button btn-modern w-full py-4 px-8 text-white text-xl font-semibold mb-6" style="background: var(--secondary-gradient);">
                    <i class="fas fa-dice mr-3"></i>
                    اقتراح وجبة عشوائية
                    <i class="fas fa-magic ml-3"></i>
                </button>

                <p id="lunchSuggestionNote" class="suggestion-note text-center text-white/60 text-sm mt-3 animate-pulse">
                    <i class="fas fa-info-circle mr-2"></i>
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة
                </p>

                <div id="suggestedLunchMeal" class="suggested-meal-card hidden glass p-6 mt-6 flex-col sm:flex-row items-center justify-between">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse flex-grow">
                        <i class="fas fa-utensils text-2xl text-red-400"></i>
                        <p class="meal-name text-2xl font-bold text-white flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    </div>
                    <button data-meal-category="lunch" class="view-details-button btn-modern px-6 py-3 text-white font-medium whitespace-nowrap" style="background: var(--primary-gradient);">
                        <i class="fas fa-eye mr-2"></i>
                        عرض التفاصيل
                    </button>
                </div>
            </div>

            <!-- Dinner Section -->
            <div id="dinnerSection" class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-moon text-2xl text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-3xl font-bold text-white mb-2">عشاء</h3>
                            <p class="text-white/70">وجبة مسائية خفيفة ومريحة</p>
                        </div>
                    </div>
                </div>

                <button data-meal-category="dinner" class="suggest-meal-button btn-modern w-full py-4 px-8 text-white text-xl font-semibold mb-6" style="background: var(--primary-gradient);">
                    <i class="fas fa-dice mr-3"></i>
                    اقتراح وجبة عشوائية
                    <i class="fas fa-magic ml-3"></i>
                </button>

                <p id="dinnerSuggestionNote" class="suggestion-note text-center text-white/60 text-sm mt-3 animate-pulse">
                    <i class="fas fa-info-circle mr-2"></i>
                    اضغط مرة أخرى على "اقتراح وجبة عشوائية" لتغيير الوجبة
                </p>

                <div id="suggestedDinnerMeal" class="suggested-meal-card hidden glass p-6 mt-6 flex-col sm:flex-row items-center justify-between">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse flex-grow">
                        <i class="fas fa-utensils text-2xl text-purple-400"></i>
                        <p class="meal-name text-2xl font-bold text-white flex-grow text-center sm:text-right mb-3 sm:mb-0"></p>
                    </div>
                    <button data-meal-category="dinner" class="view-details-button btn-modern px-6 py-3 text-white font-medium whitespace-nowrap" style="background: var(--primary-gradient);">
                        <i class="fas fa-eye mr-2"></i>
                        عرض التفاصيل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Detail Page -->
    <div id="mealDetailPage" class="page">
        <button id="backToCategoryButton" class="back-button">
            <i class="fas fa-arrow-right"></i>
            <span>الوجبات</span>
        </button>

        <!-- Meal Header -->
        <div class="glass text-center p-8 mb-12 max-w-4xl">
            <h2 id="mealDetailTitle" class="text-5xl font-black text-white mb-4"></h2>
            <div class="flex items-center justify-center space-x-6 rtl:space-x-reverse text-white/80">
                <div class="flex items-center">
                    <i class="fas fa-clock mr-2"></i>
                    <span>سريع التحضير</span>
                </div>
                <div class="w-1 h-1 bg-white/40 rounded-full"></div>
                <div class="flex items-center">
                    <i class="fas fa-heart mr-2"></i>
                    <span>صحي ولذيذ</span>
                </div>
                <div class="w-1 h-1 bg-white/40 rounded-full"></div>
                <div class="flex items-center">
                    <i class="fas fa-star mr-2"></i>
                    <span>وصفة مميزة</span>
                </div>
            </div>
        </div>

        <div class="scrollable-content">
            <!-- Meal Details -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">التفاصيل</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-info-circle text-xl text-white"></i>
                    </div>
                </div>
                <p id="mealDetails" class="text-lg text-white/90 leading-relaxed text-right"></p>
            </div>

            <!-- Ingredients -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">المكونات</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-list text-xl text-white"></i>
                    </div>
                </div>
                <ul id="mealIngredients" class="list-none p-0 m-0 space-y-3"></ul>
            </div>

            <!-- Instructions -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">طريقة التحضير</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-red-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-utensils text-xl text-white"></i>
                    </div>
                </div>
                <div id="mealInstructions" class="text-lg text-white/90 leading-relaxed text-right whitespace-pre-line"></div>
            </div>

            <!-- Calories -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">السعرات الحرارية</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-red-400 to-pink-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-fire text-xl text-white"></i>
                    </div>
                </div>
                <p id="mealCalories" class="text-lg text-white/90 leading-relaxed text-right"></p>
            </div>

            <!-- AI Feature Section -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">تعديل الوصفة بالذكاء الاصطناعي</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-robot text-xl text-white"></i>
                    </div>
                </div>
                <button id="aiModifyRecipeButton" class="btn-modern w-full py-4 px-8 text-white text-xl font-bold" style="background: var(--secondary-gradient);">
                    <i class="fas fa-magic mr-3"></i>
                    تعديل الوصفة بالذكاء الاصطناعي
                    <i class="fas fa-sparkles ml-3"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- About App Page -->
    <div id="aboutAppPage" class="page">
        <button id="backFromAboutButton" class="back-button">
            <i class="fas fa-arrow-right"></i>
            <span>الرئيسية</span>
        </button>

        <!-- Page Header -->
        <div class="glass text-center p-8 mb-12 max-w-4xl">
            <h2 class="text-5xl font-black text-white mb-4">عن التطبيق</h2>
            <p class="text-white/80 text-lg">
                <i class="fas fa-info-circle mr-2"></i>
                تعرف على مميزات وإمكانيات التطبيق
            </p>
        </div>

        <div class="scrollable-content">
            <!-- App Purpose -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">الهدف من التطبيق</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-bullseye text-xl text-white"></i>
                    </div>
                </div>
                <p class="text-lg text-white/90 leading-relaxed text-right">
                    تطبيق "هناكل إيه بكرة؟" هو رفيقك اليومي لاختيار وجباتك بمنتهى السهولة والمرونة! سواء كنت تتبع حمية غذائية صارمة (دايت) أو تفضل الوجبات العادية، يوفر لك التطبيق مجموعة واسعة من الاقتراحات اللذيذة والمغذية مع إمكانية التخصيص باستخدام الذكاء الاصطناعي.
                </p>
            </div>

            <!-- Features -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">المميزات</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-red-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-star text-xl text-white"></i>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="glass p-4 text-center">
                        <i class="fas fa-leaf text-3xl text-green-400 mb-3"></i>
                        <h4 class="text-lg font-bold text-white mb-2">وجبات دايت</h4>
                        <p class="text-white/70 text-sm">وجبات صحية ومتوازنة</p>
                    </div>
                    <div class="glass p-4 text-center">
                        <i class="fas fa-pizza-slice text-3xl text-orange-400 mb-3"></i>
                        <h4 class="text-lg font-bold text-white mb-2">وجبات عادية</h4>
                        <p class="text-white/70 text-sm">وجبات لذيذة ومتنوعة</p>
                    </div>
                    <div class="glass p-4 text-center">
                        <i class="fas fa-dice text-3xl text-purple-400 mb-3"></i>
                        <h4 class="text-lg font-bold text-white mb-2">اقتراحات عشوائية</h4>
                        <p class="text-white/70 text-sm">وجبات مختلفة في كل مرة</p>
                    </div>
                    <div class="glass p-4 text-center">
                        <i class="fas fa-robot text-3xl text-blue-400 mb-3"></i>
                        <h4 class="text-lg font-bold text-white mb-2">ذكاء اصطناعي</h4>
                        <p class="text-white/70 text-sm">تعديل الوصفات حسب رغبتك</p>
                    </div>
                </div>
            </div>

            <!-- How to Use -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">كيفية الاستخدام</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-mobile-alt text-xl text-white"></i>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">1</div>
                        <p class="text-white/90">اختر نوع الوجبة (دايت أو عادي)</p>
                    </div>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">2</div>
                        <p class="text-white/90">اختر وقت الوجبة (فطار، غداء، عشاء)</p>
                    </div>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-gradient-to-r from-pink-400 to-red-500 rounded-full flex items-center justify-center text-white font-bold">3</div>
                        <p class="text-white/90">اضغط على "اقتراح وجبة عشوائية"</p>
                    </div>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-gradient-to-r from-red-400 to-yellow-500 rounded-full flex items-center justify-center text-white font-bold">4</div>
                        <p class="text-white/90">اضغط على "عرض التفاصيل" لرؤية الوصفة كاملة</p>
                    </div>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-green-500 rounded-full flex items-center justify-center text-white font-bold">5</div>
                        <p class="text-white/90">استخدم الذكاء الاصطناعي لتعديل الوصفة حسب رغبتك</p>
                    </div>
                </div>
            </div>

            <!-- Developer Info -->
            <div class="glass p-8 mb-8 hover:scale-105 transition-all duration-300">
                <div class="flex items-center justify-end mb-6 space-x-3 rtl:space-x-reverse">
                    <h3 class="text-3xl font-bold text-white">معلومات المطور</h3>
                    <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-teal-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user-code text-xl text-white"></i>
                    </div>
                </div>

                <div class="text-center mb-6">
                    <div class="w-20 h-20 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-laptop-code text-3xl text-white"></i>
                    </div>
                    <h4 class="text-2xl font-bold text-white mb-2">Kareem Waheeb</h4>
                    <p class="text-white/70 text-lg">مطور تطبيقات الويب</p>
                </div>

                <div class="text-center">
                    <p class="text-white/90 mb-6 text-lg">
                        للتواصل والاستفسارات أو طلب تطوير تطبيقات مشابهة
                    </p>

                    <a href="https://wa.me/201159296333" target="_blank" class="btn-modern inline-flex items-center px-8 py-4 text-white text-lg font-bold hover:scale-110 transition-all duration-300" style="background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);">
                        <i class="fab fa-whatsapp text-2xl mr-3"></i>
                        تواصل عبر الواتساب
                        <i class="fas fa-external-link-alt ml-3"></i>
                    </a>

                    <p class="text-white/60 text-sm mt-4">
                        <i class="fas fa-phone mr-2"></i>
                        01159296333
                    </p>
                </div>
            </div>
        </div>
    </div>


    <!-- AI Modification Modal -->
    <div id="aiModal" class="modal-overlay hidden">
        <div class="modal-content glass">
            <div class="flex justify-between items-center mb-6">
                <button class="back-button" id="backFromAiModalButton">
                    <i class="fas fa-arrow-right"></i>
                    <span>رجوع</span>
                </button>
                <button class="close-button" id="closeModalButton">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-robot text-2xl text-white"></i>
                </div>
                <h3 class="text-3xl font-bold text-white mb-2">عدّل وصفتك بالذكاء الاصطناعي</h3>
                <p class="text-white/70">اطلب أي تعديل تريده على الوصفة</p>
            </div>

            <div class="glass p-4 mb-4">
                <textarea id="aiPromptInput" class="w-full p-4 bg-transparent text-white placeholder-white/60 border border-white/20 rounded-lg text-right resize-none focus:outline-none focus:border-white/40" rows="4" placeholder="مثال: اجعلها نباتية، أضف المزيد من البروتين، قلل السعرات الحرارية، اجعلها خالية من الجلوتين..."></textarea>
            </div>

            <button id="sendPromptButton" class="btn-modern w-full py-4 px-8 text-white text-lg font-bold mb-4" style="background: var(--secondary-gradient);">
                <i class="fas fa-paper-plane mr-3"></i>
                أرسل الطلب
                <i class="fas fa-magic ml-3"></i>
            </button>

            <div id="aiLoadingIndicator" class="flex justify-center items-center py-6 hidden">
                <div class="loading-spinner mr-4"></div>
                <span class="text-white/90 text-lg">الذكاء الاصطناعي يفكر...</span>
            </div>

            <div id="aiResponseArea" class="glass p-6 text-white text-lg ai-response-area min-h-[120px] max-h-[400px] overflow-y-auto">
                <div class="text-center text-white/60">
                    <i class="fas fa-comment-dots text-3xl mb-3"></i>
                    <p>ستظهر استجابة الذكاء الاصطناعي هنا</p>
                </div>
            </div>

            <p id="aiErrorDisplay" class="text-red-400 text-sm mt-4 hidden text-right bg-red-500/10 p-3 rounded-lg border border-red-500/20"></p>
        </div>
    </div>

    <script>
        // --- Lists of specific meal names for better variety ---
        const dietBreakfastNames = [
            "شوفان بالتوت واللوز", "بيض مسلوق مع سبانخ وطماطم", "زبادي يوناني مع الفاكهة والعسل", "ساندويتش جبنة قريش بالخيار",
            "سموثي أخضر بالكيوي والسبانخ", "توست أفوكادو بالبيض المسلوق", "فطائر الشوفان الصحية بالتفاح", "جرانولا منزلية بالفواكه المجففة",
            "سموثي البروتين بالموز وزبدة الفول السوداني", "فول مدمس خفيف بزيت الزيتون والكمون", "بان كيك الشوفان", "أومليت الخضروات"
        ];
        const dietLunchNames = [
            "سلطة دجاج مشوي بالخضروات الورقية", "سمك سلمون مشوي مع أرز بسمتي بني", "شوربة عدس بالخضروات المشكلة", "خضار سوتيه وصدر دجاج بالليمون",
            "سلطة الكينوا بالخضروات والحمص", "تونا بالخضروات الطازجة وصوص الزبادي", "رول الخس بالدجاج والخضار", "عدس بجبنة البارميزان الخفيفة",
            "بوريتو بول صحي بالدجاج والأرز البري", "خضروات مشوية وحمص بالليمون والثوم", "شوربة الطماطم بالريحان", "فيليه دجاج مشوي"
        ];
        const dietDinnerNames = [
            "جبنة قريش بالخضروات المقطعة وزيت الزيتون", "سلطة تونة لايت بالذرة والفلفل", "صدر دجاج مسلوق مع بروكلي على البخار", "شوربة بروكلي بالكريمة الخفيفة",
            "بيض أومليت خفيف بالسبانخ والفطر", "زبادي وخيار بالنعناع", "خضروات مشوية خفيفة بالبهارات", "سمك فيليه على البخار مع الليمون",
            "سلطة فواكه بالزبادي قليل الدسم", "دجاج مسلوق وخضروات مسلوقة", "سلطة يونانية خفيفة", "شوربة الخضار بالكريمة"
        ];

        const normalBreakfastNames = [
            "فول بالزيت والليمون مع البيض المدحرج", "بيض بالبسطرمة والجبنة الموتزاريلا", "مناقيش جبنة وزعتر طازجة من الفرن", "شكشوكة بالبيض والطماطم والفلفل",
            "ساندويتشات الجبن الساخن بالزبدة", "فطائر باللحمة المفرومة والبصل", "خبز بالبيض والجبن الشيدر", "عجة البيض بالخضروات والبهارات",
            "حواوشي فطار باللحم المفروم والخبز البلدي", "بيض بالنقانق والبطاطس المقلية", "طعمية سخنة", "مربى وقشطة وعسل"
        ];
        const normalLunchNames = [
            "مكرونة بالبشاميل واللحم المفروم الغني", "محاشي مشكلة (كوسة، باذنجان، فلفل) بالأرز والخلطة السرية", "أرز بالخضار وقطع الدجاج المتبلة", "كشري مصري بجميع مكوناته",
            "فتة شاورما الدجاج بالثومية والعيش المقلي", "كفتة بالطحينة وصوص الطماطم", "طواجن لحمة بالبطاطس في الفرن", "مبكبكة باللحم المتبل والمكرونة",
            "أرز معمر باللحم والزبدة", "صيادية السمك البوري بالصوص البني", "كبدة إسكندراني بالردة", "فته كوارع"
        ];
        const normalDinnerNames = [
            "حواوشي إسكندراني بالجبنة", "بطاطس محمرة وبرجر لحم بالجبنة", "كشري حلة واحدة سريع ولذيذ", "فطير مشلتت بالعسل الأسود والطحينة",
            "صينية دجاج بالبطاطس والجزر في الفرن", "شوربة سي فود بالكريمة والمأكولات البحرية", "معكرونة بالصلصة الحمراء واللحم المفروم", "بيتزا منزلية بالخضروات والجبن",
            "شيش طاووق على الفحم مع الأرز المبهر", "ساندويتشات كبدة بالعيش الفينو", "كفتة الأرز بالصلصة", "ساندويتشات التونة"
        ];

        // --- Data Generation ---
        const generateMeals = (nameList, isDiet, type) => {
            const meals = [];
            nameList.forEach((baseMealName, i) => {
                let name = baseMealName;
                let details = `وجبة ${isDiet ? 'صحية ' : 'شهية '} ولذيذة، مثالية لوجبة ${type}. يمكنك الاستمتاع بها كخيار سريع ومغذي.`;
                let ingredients = [];
                let instructions = '';
                let caloriesPer100g;
                let servingSizeGrams;

                if (type === 'breakfast') {
                    if (isDiet) {
                        ingredients = ["1 كوب شوفان", "1/2 كوب حليب قليل الدسم", "1 ملعقة كبيرة عسل", "1/4 كوب مكسرات مشكلة"];
                    } else {
                        ingredients = ["2 بيضة", "1 رغيف خبز بلدي", "1 ملعقة كبيرة زبدة", "1 كوب شاي بالحليب"];
                    }
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (200 - 150 + 1)) + 150 : Math.floor(Math.random() * (250 - 200 + 1)) + 200;
                    servingSizeGrams = Math.floor(Math.random() * (200 - 150 + 1)) + 150;
                } else if (type === 'lunch') {
                    if (isDiet) {
                        ingredients = ["150 جرام صدر دجاج مشوي", "1 كوب أرز بني", "2 كوب سلطة خضراء", "1 ملعقة كبيرة زيت زيتون"];
                    } else {
                        ingredients = ["200 جرام لحم أو دجاج", "2 كوب أرز أبيض", "1 كوب خضروات مطبوخة", "1 رغيف خبز"];
                    }
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (250 - 200 + 1)) + 200 : Math.floor(Math.random() * (350 - 300 + 1)) + 300;
                    servingSizeGrams = Math.floor(Math.random() * (300 - 200 + 1)) + 200;
                } else if (type === 'dinner') {
                    if (isDiet) {
                        ingredients = ["1 كوب زبادي قليل الدسم", "1 خيار متوسط", "1 طماطم", "رشة زيت زيتون وزعتر"];
                    } else {
                        ingredients = ["100 جرام جبنة بيضاء", "1 رغيف خبز", "1 طماطم", "1 كوب شاي"];
                    }
                    caloriesPer100g = isDiet ? Math.floor(Math.random() * (180 - 120 + 1)) + 120 : Math.floor(Math.random() * (280 - 220 + 1)) + 220;
                    servingSizeGrams = Math.floor(Math.random() * (250 - 150 + 1)) + 150;
                }

                if (baseMealName.includes('شوفان')) {
                    ingredients = ["1/2 كوب شوفان", "1 كوب ماء/حليب قليل الدسم", "1/2 كوب توت مشكل", "ملعقة صغيرة عسل/مكسرات"];
                    instructions = `لتحضير ${name}:\n1. اخلط نصف كوب شوفان مع كوب ماء أو حليب قليل الدسم في قدر.\n2. سخّن على نار متوسطة مع التحريك المستمر حتى يتكثف.\n3. أضف التوت والمكسرات (مثل اللوز) وملعقة صغيرة من العسل أو بديل السكر.\n4. قدّمها ساخنة واستمتع بوجبة فطار غنية بالألياف.`;
                    caloriesPer100g = isDiet ? 150 : 200;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('بيض مسلوق')) {
                    ingredients = ["2 بيضة مسلوقة", "1 كوب سبانخ طازجة", "1 طماطم متوسطة"];
                    instructions = `لتحضير ${name}:\n1. اسلق البيض لمدة 7-10 دقائق.\n2. قطّع السبانخ والطماطم.\n3. قشّر البيض وقطّعه.\n4. اخلط المكونات مع رشة ملح وفلفل.`;
                    caloriesPer100g = isDiet ? 155 : 180;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('زبادي')) {
                    ingredients = ["1 كوب زبادي يوناني قليل الدسم", "1/2 كوب فراولة مقطعة", "1/4 كوب كيوي مقطع", "ملعقة صغيرة بذور شيا"];
                    instructions = `لتحضير ${name}:\n1. ضع الزبادي في وعاء.\n2. أضف الفاكهة المقطعة فوق الزبادي.\n3. رش بذور الشيا.\n4. استمتع بوجبة خفيفة ومنعشة.`;
                    caloriesPer100g = isDiet ? 60 : 80;
                    servingSizeGrams = 250;
                } else if (baseMealName.includes('بان كيك الشوفان')) {
                    ingredients = ["1 كوب شوفان مطحون", "1 موزة مهروسة", "2 بيضة", "1/2 كوب حليب", "1 ملعقة صغيرة بيكنج باودر", "رشة قرفة"];
                    instructions = `لتحضير ${name}:\n1. اخلط الشوفان المطحون مع البيكنج باودر والقرفة.\n2. اهرس الموزة وأضف البيض والحليب واخفق جيداً.\n3. أضف خليط المكونات السائلة إلى الجافة.\n4. سخّن مقلاة واسكب ربع كوب من الخليط لكل بان كيك.\n5. اطهيها لمدة 2-3 دقائق على كل جانب.`;
                    caloriesPer100g = isDiet ? 180 : 220;
                    servingSizeGrams = 180;
                } else if (baseMealName.includes('أومليت الخضروات')) {
                    ingredients = ["2 بيضة", "1/4 كوب فلفل ألوان مقطع", "1/4 كوب فطر مقطع", "قليل من البصل المفروم", "ملح وفلفل", "زيت زيتون"];
                    instructions = `لتحضير ${name}:\n1. اخفق البيض مع الملح والفلفل.\n2. سخّن زيت الزيتون في مقلاة وشوّح الخضروات.\n3. اسكب البيض فوق الخضروات واتركه ينضج.`;
                    caloriesPer100g = isDiet ? 130 : 160;
                    servingSizeGrams = 160;
                } else if (baseMealName.includes('سلطة دجاج مشوي')) {
                    ingredients = ["150 جرام صدر دجاج مشوي", "2 كوب خس متنوع", "1 طماطم كبيرة", "1/2 خيار", "صلصة سلطة لايت"];
                    instructions = `لتحضير ${name}:\n1. اشوي صدر الدجاج وقطّعه.\n2. اخلط الخضروات في وعاء كبير.\n3. أضف الدجاج إلى السلطة.\n4. حضّر صلصة لايت واسكبها فوق السلطة.`;
                    caloriesPer100g = isDiet ? 120 : 150;
                    servingSizeGrams = 350;
                } else if (baseMealName.includes('سمك') && type === 'lunch') {
                    ingredients = ["150 جرام فيليه سمك", "1/2 كوب أرز بني مطبوخ", "1 كوب بروكلي مطبوخ", "شريحة ليمون"];
                    instructions = `لتحضير ${name}:\n1. اشوِ فيليه السمك في الفرن.\n2. اسلق الأرز البني والبروكلي.\n3. قدّم السمك مع الأرز والبروكلي.`;
                    caloriesPer100g = isDiet ? 140 : 170;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('شوربة الطماطم بالريحان')) {
                    ingredients = ["4 حبات طماطم كبيرة", "1 بصلة متوسطة", "2 فص ثوم", "1/4 كوب ريحان طازج", "2 كوب مرق خضروات", "ملح وفلفل"];
                    instructions = `لتحضير ${name}:\n1. شوّح البصل والثوم.\n2. أضف الطماطم ومرق الخضروات واتركها تغلي.\n3. اخلط الشوربة في الخلاط.\n4. أعدها للقدر وأضف الريحان والبهارات.`;
                    caloriesPer100g = isDiet ? 50 : 70;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('فيليه دجاج مشوي')) {
                    ingredients = ["150 جرام فيليه دجاج", "تتبيلة (ليمون، ثوم، أعشاب)", "خضروات مشوية"];
                    instructions = `لتحضير ${name}:\n1. تبّل فيليه الدجاج.\n2. اشوي الدجاج على الشواية.\n3. قدمه مع الخضروات المشوية.`;
                    caloriesPer100g = isDiet ? 165 : 190;
                    servingSizeGrams = 220;
                } else if (baseMealName.includes('مكرونة بالبشاميل')) {
                    ingredients = ["200 جرام مكرونة قلم", "250 جرام لحم مفروم", "500 مل بشاميل", "جبنة موتزاريلا"];
                    instructions = `لتحضير ${name}:\n1. اسلق المكرونة.\n2. حضّر اللحم المفروم المعصّج.\n3. حضّر صلصة البشاميل.\n4. رص المكونات في صينية وأدخلها الفرن.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 450;
                } else if (baseMealName.includes('جبنة قريش') && type === 'dinner') {
                    ingredients = ["100 جرام جبنة قريش", "1 طماطم صغيرة", "1/2 فلفل أخضر", "رشة زيت زيتون وزعتر"];
                    instructions = `لتحضير ${name}:\n1. ضع الجبنة القريش في طبق.\n2. قطّع الخضروات وأضفها فوق الجبنة.\n3. رش زيت الزيتون والزعتر.`;
                    caloriesPer100g = isDiet ? 98 : 120;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('سلطة يونانية خفيفة')) {
                    ingredients = ["1 خيار", "1 طماطم", "1/2 بصلة حمراء", "زيتون كالاماتا", "50 جرام جبنة فيتا قليلة الدسم", "صلصة (زيت زيتون، ليمون، أوريجانو)"];
                    instructions = `لتحضير ${name}:\n1. قطّع الخضروات.\n2. أضف الزيتون وجبنة الفيتا.\n3. حضّر الصلصة واسكبها فوق السلطة.`;
                    caloriesPer100g = isDiet ? 90 : 120;
                    servingSizeGrams = 280;
                } else if (baseMealName.includes('شوربة الخضار بالكريمة')) {
                    ingredients = ["1 كوب خضروات مشكلة", "1 بصلة مفرومة", "2 كوب مرق دجاج", "1/2 كوب كريمة طبخ لايت", "ملح وفلفل"];
                    instructions = `لتحضير ${name}:\n1. شوّح البصل وأضف الخضروات والمرق.\n2. اتركها تغلي حتى تنضج الخضروات.\n3. اخلط نصف الشوربة وأعدها للقدر.\n4. أضف الكريمة والبهارات.`;
                    caloriesPer100g = isDiet ? 80 : 110;
                    servingSizeGrams = 280;
                } else if (baseMealName.includes('حواوشي إسكندراني')) {
                    ingredients = ["2 رغيف خبز بلدي", "200 جرام لحم مفروم", "1 بصلة كبيرة مفرومة", "2 فلفل أخضر مفروم", "بهارات حواوشي", "100 جرام جبنة موتزاريلا"];
                    instructions = `لتحضير ${name}:\n1. اخلط اللحم المفروم مع البصل والفلفل والبهارات.\n2. احشي الخبز بالخليط وأضف الجبنة.\n3. اخبزه في الفرن حتى ينضج ويصبح ذهبياً.`;
                    caloriesPer100g = 320;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('حواوشي') && !baseMealName.includes('إسكندراني')) {
                    ingredients = ["1 رغيف خبز بلدي", "150 جرام لحم مفروم", "بصل مفروم", "فلفل أخضر", "بهارات حواوشي"];
                    instructions = `لتحضير ${name}:\n1. اخلط اللحم المفروم مع البصل والفلفل والبهارات.\n2. احشي الخبز بالخليط.\n3. اخبزه في الفرن حتى ينضج.`;
                    caloriesPer100g = 350;
                    servingSizeGrams = 250;
                } else if (baseMealName.includes('طعمية سخنة')) {
                    ingredients = ["1 كوب فول مدشوش منقوع", "1/2 حزمة كزبرة خضراء", "1 بصلة صغيرة", "2 فص ثوم", "كمون، ملح", "زيت للقلي"];
                    instructions = `لتحضير ${name}:\n1. افرم الفول مع الخضرة والبصل والثوم.\n2. أضف البهارات وشكّل العجينة.\n3. اقليها في زيت غزير وساخن.`;
                    caloriesPer100g = 250;
                    servingSizeGrams = 150;
                } else if (baseMealName.includes('كبدة إسكندراني بالردة')) {
                    ingredients = ["250 جرام كبدة شرائح", "1/2 كوب ردة", "فلفل أخضر حار", "ثوم مفروم", "كمون، ملح", "زيت للقلي"];
                    instructions = `لتحضير ${name}:
1. تبّل الكبدة بالثوم والبهارات.
2. غطّها بالردة واقليها في الزيت.
3. شوّح الفلفل وقدمه مع الكبدة.`;
                    caloriesPer100g = 300;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('فتة كوارع')) {
                    ingredients = ["1 زوج كوارع منظف", "2 كوب أرز مصري", "3 أرغفة خبز بلدي", "4 فصوص ثوم", "1/2 كوب خل", "صلصة طماطم", "سمن"];
                    instructions = `لتحضير ${name}:
1. اسلق الكوارع جيداً مع البهارات (ورق لورا، حبهان) حتى تنضج تماماً.
2. حمّص الخبز المقطع في السمن حتى يصبح ذهبياً.
3. حضّر الأرز الأبيض المفلفل.
4. حضّر صلصة الفتة: شوّح الثوم في السمن، أضف الخل ثم صلصة الطماطم واتركها تتسبك.
5. في طبق التقديم، ضع الخبز المحمص، اسقه بقليل من شوربة الكوارع، ثم ضع الأرز، ثم الصلصة، وأخيراً قطع الكوارع.`;
                    caloriesPer100g = 320;
                    servingSizeGrams = 500;
                } else if (baseMealName.includes('بطاطس محمرة وبرجر')) {
                    ingredients = ["3 حبات بطاطس كبيرة", "2 قطعة برجر لحم", "100 جرام جبنة شيدر", "زيت للقلي", "ملح وفلفل", "خس وطماطم"];
                    instructions = `لتحضير ${name}:
1. قطّع البطاطس واقليها حتى تصبح ذهبية.
2. اشوي البرجر وضع عليه الجبنة.
3. قدم البرجر مع البطاطس المحمرة والخضروات.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 350;
                } else if (baseMealName.includes('كشري حلة واحدة')) {
                    ingredients = ["1 كوب أرز", "1/2 كوب عدس أسود", "1/2 كوب مكرونة", "2 بصلة كبيرة", "صلصة طماطم", "شطة وثوم"];
                    instructions = `لتحضير ${name}:
1. اسلق الأرز والعدس والمكرونة كل على حدة.
2. حمّر البصل حتى يصبح ذهبياً.
3. حضّر الصلصة والشطة وقدم الكشري مع التقليات.`;
                    caloriesPer100g = 250;
                    servingSizeGrams = 400;
                } else if (baseMealName.includes('فطير مشلتت')) {
                    ingredients = ["2 كوب دقيق", "1/2 كوب زيت", "ماء دافئ", "ملح", "عسل أسود", "طحينة"];
                    instructions = `لتحضير ${name}:
1. اعجن الدقيق بالزيت والماء والملح.
2. افرد العجين رقيقاً واطبخه على الطاوة.
3. قدمه مع العسل الأسود والطحينة.`;
                    caloriesPer100g = 300;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('صينية دجاج بالبطاطس')) {
                    ingredients = ["4 قطع دجاج", "4 حبات بطاطس", "2 جزر", "بصلة كبيرة", "بهارات مشكلة", "زيت زيتون"];
                    instructions = `لتحضير ${name}:
1. تبّل قطع الدجاج بالبهارات.
2. قطّع الخضروات وضعها في صينية الفرن.
3. أضف الدجاج واخبز لمدة 45 دقيقة.`;
                    caloriesPer100g = 220;
                    servingSizeGrams = 400;
                } else if (baseMealName.includes('شوربة سي فود')) {
                    ingredients = ["200 جرام مأكولات بحرية مشكلة", "1/2 كوب كريمة طبخ", "1 بصلة", "2 فص ثوم", "مرق سمك", "بهارات"];
                    instructions = `لتحضير ${name}:
1. شوّح البصل والثوم في الزيت.
2. أضف المأكولات البحرية والمرق.
3. أضف الكريمة والبهارات واتركها تنضج.`;
                    caloriesPer100g = 180;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('معكرونة بالصلصة الحمراء')) {
                    ingredients = ["300 جرام مكرونة", "200 جرام لحم مفروم", "صلصة طماطم", "بصلة", "ثوم", "جبنة بارميزان"];
                    instructions = `لتحضير ${name}:
1. اسلق المكرونة حتى تنضج.
2. حضّر اللحم المفروم مع البصل والثوم.
3. أضف الصلصة وقدم مع الجبنة.`;
                    caloriesPer100g = 260;
                    servingSizeGrams = 350;
                } else if (baseMealName.includes('بيتزا منزلية')) {
                    ingredients = ["عجينة بيتزا جاهزة", "صلصة طماطم", "جبنة موتزاريلا", "خضروات مشكلة", "زيتون", "فلفل ألوان"];
                    instructions = `لتحضير ${name}:
1. افرد العجينة وادهنها بالصلصة.
2. أضف الجبنة والخضروات.
3. اخبزها في الفرن حتى تنضج.`;
                    caloriesPer100g = 240;
                    servingSizeGrams = 250;
                } else if (baseMealName.includes('شيش طاووق')) {
                    ingredients = ["500 جرام صدر دجاج مقطع مكعبات", "زبادي", "ثوم", "بهارات شيش طاووق", "أرز مبهر", "سلطة"];
                    instructions = `لتحضير ${name}:
1. تبّل الدجاج بالزبادي والثوم والبهارات.
2. اشويه على الفحم أو الشواية.
3. قدمه مع الأرز المبهر والسلطة.`;
                    caloriesPer100g = 200;
                    servingSizeGrams = 300;
                } else if (baseMealName.includes('ساندويتشات كبدة')) {
                    ingredients = ["250 جرام كبدة مقطعة", "خبز فينو", "فلفل أخضر حار", "ثوم", "كمون", "طحينة"];
                    instructions = `لتحضير ${name}:
1. تبّل الكبدة وقليها سريعاً.
2. شوّح الفلفل والثوم.
3. احشي الخبز بالكبدة والطحينة.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 200;
                } else if (baseMealName.includes('ساندويتشات التونة')) {
                    ingredients = ["1 علبة تونة مصفاة", "2 ملعقة كبيرة مايونيز", "1/4 بصلة صغيرة مفرومة", "فلفل ألوان مقطع", "خبز فينو أو توست"];
                    instructions = `لتحضير ${name}:
1. اخلط التونة مع المايونيز والبصل والفلفل.
2. أضف الملح والفلفل حسب الرغبة.
3. احشي الخبز بخليط التونة وقدمها.`;
                    caloriesPer100g = 220;
                    servingSizeGrams = 180;
                } else if (baseMealName.includes('كفتة الأرز بالصلصة')) {
                    ingredients = ["1/2 كيلو لحم مفروم", "1 كوب أرز مطحون", "خضرة (شبت، بقدونس، كزبرة)", "1 بصلة", "صلصة طماطم", "زيت للقلي"];
                    instructions = `لتحضير ${name}:
1. اخلط اللحم مع الأرز المطحون والخضرة والبصل المفروم والبهارات.
2. شكّل الخليط على هيئة أصابع.
3. اقلي الكفتة في الزيت حتى تتحمر.
4. حضّر صلصة الطماطم وضع فيها الكفتة المقلية واتركها على نار هادئة لتتسبك.`;
                    caloriesPer100g = 280;
                    servingSizeGrams = 300;
                } else {
                    // إضافة طرق تحضير مخصصة للوجبات المتبقية
                    if (baseMealName.includes('ساندويتش جبنة قريش')) {
                        ingredients = ["100 جرام جبنة قريش", "2 شريحة خبز أسمر", "1 خيار صغير", "1 طماطم صغيرة", "رشة ملح وفلفل"];
                        instructions = `لتحضير ${name}:
1. قطّع الخيار إلى شرائح رفيعة.
2. ادهن الخبز بالجبنة القريش.
3. ضع شرائح الخيار والطماطم.
4. أضف رشة ملح وفلفل أسود وقدمها.`;
                    } else if (baseMealName.includes('سموثي أخضر')) {
                        ingredients = ["2 حبة كيوي", "1 كوب سبانخ طازجة", "1 كوب ماء أو حليب قليل الدسم", "1 ملعقة صغيرة عسل"];
                        instructions = `لتحضير ${name}:
1. ضع الكيوي والسبانخ في الخلاط.
2. أضف الماء أو الحليب قليل الدسم.
3. اخلط جيداً حتى يصبح ناعماً.
4. قدمه بارداً في كوب طويل.`;
                    } else if (baseMealName.includes('توست أفوكادو')) {
                        ingredients = ["1 حبة أفوكادو ناضجة", "2 شريحة خبز أسمر", "1 بيضة مسلوقة", "رشة ملح وفلفل أسود", "قطرات ليمون"];
                        instructions = `لتحضير ${name}:
1. اهرس الأفوكادو بالشوكة.
2. حمّص الخبز حتى يصبح ذهبياً.
3. ادهن الأفوكادو على التوست.
4. ضع البيض المسلوق المقطع فوقه.`;
                    } else if (baseMealName.includes('فطائر الشوفان الصحية')) {
                        ingredients = ["1 كوب شوفان", "1 تفاحة مبشورة", "1 بيضة", "1/2 كوب حليب", "1 ملعقة صغيرة قرفة", "1 ملعقة صغيرة زيت"];
                        instructions = `لتحضير ${name}:
1. اخلط الشوفان مع التفاح المبشور والقرفة.
2. أضف البيض والحليب واخلط جيداً.
3. اسكب الخليط في مقلاة مدهونة بقليل من الزيت.
4. اطهيها على نار متوسطة حتى تنضج.`;
                    } else if (baseMealName.includes('جرانولا منزلية')) {
                        ingredients = ["2 كوب شوفان", "1/2 كوب مكسرات مشكلة", "1/4 كوب عسل", "1/4 كوب فواكه مجففة", "1 ملعقة صغيرة فانيليا"];
                        instructions = `لتحضير ${name}:
1. اخلط الشوفان مع المكسرات والعسل.
2. ضع الخليط في صينية فرن.
3. اخبزها لمدة 15-20 دقيقة مع التحريك.
4. أضف الفواكه المجففة بعد التبريد.`;
                    } else if (baseMealName.includes('سموثي البروتين')) {
                        ingredients = ["1 موزة ناضجة", "2 ملعقة كبيرة زبدة فول سوداني", "1 كوب حليب", "1 ملعقة كبيرة مسحوق بروتين", "مكعبات ثلج"];
                        instructions = `لتحضير ${name}:
1. ضع الموز وزبدة الفول السوداني في الخلاط.
2. أضف الحليب ومسحوق البروتين.
3. اخلط حتى يصبح كريمياً.
4. قدمه بارداً مع مكعبات الثلج.`;
                    } else if (baseMealName.includes('فول مدمس خفيف')) {
                        ingredients = ["1 كوب فول مدمس", "2 ملعقة كبيرة زيت زيتون", "1 ملعقة صغيرة كمون", "ملح وفلفل", "خضروات طازجة للتقديم"];
                        instructions = `لتحضير ${name}:
1. اسلق الفول حتى ينضج تماماً.
2. اهرسه قليلاً بالشوكة.
3. أضف زيت الزيتون والكمون والملح.
4. قدمه ساخناً مع الخضروات الطازجة.`;
                    } else if (baseMealName.includes('سلطة الكينوا')) {
                        ingredients = ["1 كوب كينوا", "1 كوب حمص مسلوق", "1 خيار مقطع", "1 طماطم مقطعة", "1/4 كوب بقدونس مفروم", "صلصة ليمون وزيت زيتون"];
                        instructions = `لتحضير ${name}:
1. اسلق الكينوا في الماء المغلي لمدة 15 دقيقة.
2. اتركها تبرد ثم أضف الخضروات المقطعة.
3. أضف الحمص المسلوق.
4. حضّر صلصة الليمون وزيت الزيتون واسكبها فوق السلطة.`;
                    } else if (baseMealName.includes('تونا بالخضروات')) {
                        instructions = `لتحضير ${name}:
1. صفّي التونة من الزيت أو الماء.
2. قطّع الخضروات إلى قطع صغيرة.
3. اخلط التونة مع الخضروات.
4. أضف صوص الزبادي والبهارات.`;
                    } else if (baseMealName.includes('رول الخس')) {
                        instructions = `لتحضير ${name}:
1. اسلق الدجاج وقطّعه إلى شرائح.
2. حضّر الخضروات المقطعة.
3. ضع الحشوة في أوراق الخس.
4. لف الأوراق بعناية وقدمها.`;
                    } else if (baseMealName.includes('عدس بجبنة البارميزان')) {
                        instructions = `لتحضير ${name}:
1. اسلق العدس حتى ينضج.
2. أضف البهارات والملح.
3. قدمه في أطباق التقديم.
4. رش جبنة البارميزان المبشورة فوقه.`;
                    } else if (baseMealName.includes('بوريتو بول صحي')) {
                        instructions = `لتحضير ${name}:
1. حضّر الأرز البري المطبوخ.
2. اشوي الدجاج وقطّعه.
3. حضّر الخضروات الطازجة.
4. رتب المكونات في وعاء وقدمها.`;
                    } else if (baseMealName.includes('خضروات مشوية وحمص')) {
                        instructions = `لتحضير ${name}:
1. قطّع الخضروات إلى قطع متوسطة.
2. ادهنها بزيت الزيتون والبهارات.
3. اشويها في الفرن لمدة 25-30 دقيقة.
4. قدمها مع الحمص المسلوق والليمون.`;
                    } else if (baseMealName.includes('بيض أومليت خفيف')) {
                        instructions = `لتحضير ${name}:
1. اخفق البيض مع قليل من الحليب.
2. سخّن المقلاة بقليل من الزيت.
3. أضف السبانخ والفطر واتركهم ينضجوا.
4. اسكب البيض واتركه ينضج ثم اطوه.`;
                    } else if (baseMealName.includes('زبادي وخيار بالنعناع')) {
                        instructions = `لتحضير ${name}:
1. قطّع الخيار إلى مكعبات صغيرة.
2. اخلطه مع الزبادي.
3. أضف النعناع المفروم والملح.
4. قدمه بارداً كوجبة خفيفة.`;
                    } else if (baseMealName.includes('خضروات مشوية خفيفة')) {
                        instructions = `لتحضير ${name}:
1. قطّع الخضروات (كوسة، باذنجان، فلفل).
2. ادهنها بزيت الزيتون والبهارات.
3. اشويها على الشواية أو في الفرن.
4. قدمها ساخنة مع رشة أعشاب طازجة.`;
                    } else if (baseMealName.includes('سمك فيليه على البخار')) {
                        instructions = `لتحضير ${name}:
1. تبّل فيليه السمك بالملح والفلفل.
2. ضعه في سلة البخار.
3. اطهيه على البخار لمدة 10-15 دقيقة.
4. قدمه مع شرائح الليمون.`;
                    } else if (baseMealName.includes('سلطة فواكه بالزبادي')) {
                        instructions = `لتحضير ${name}:
1. قطّع الفواكه إلى قطع متوسطة.
2. ضعها في وعاء كبير.
3. أضف الزبادي قليل الدسم.
4. اخلط برفق وقدمها باردة.`;
                    } else if (baseMealName.includes('دجاج مسلوق وخضروات')) {
                        instructions = `لتحضير ${name}:
1. اسلق الدجاج مع البهارات حتى ينضج.
2. اسلق الخضروات في ماء منفصل.
3. قطّع الدجاج إلى قطع.
4. قدمه مع الخضروات المسلوقة.`;
                    } else {
                        // للوجبات العادية، نضيف طرق تحضير أكثر تفصيلاً
                        if (type === 'breakfast') {
                            if (baseMealName.includes('فول بالزيت')) {
                                instructions = `لتحضير ${name}:
1. اسلق الفول حتى ينضج تماماً.
2. اهرسه قليلاً وأضف زيت الزيتون والليمون.
3. حضّر البيض المدحرج في مقلاة منفصلة.
4. قدم الفول مع البيض والخبز البلدي.`;
                            } else if (baseMealName.includes('بيض بالبسطرمة')) {
                                instructions = `لتحضير ${name}:
1. قطّع البسطرمة إلى شرائح.
2. اقليها في المقلاة حتى تصبح مقرمشة.
3. أضف البيض واخفقه مع البسطرمة.
4. أضف الجبنة الموتزاريلا واتركها تذوب.`;
                            } else if (baseMealName.includes('مناقيش جبنة وزعتر')) {
                                instructions = `لتحضير ${name}:
1. حضّر عجينة المناقيش أو استخدم الجاهزة.
2. ادهن العجينة بزيت الزيتون.
3. رش الجبنة والزعتر بسخاء.
4. اخبزها في الفرن حتى تصبح ذهبية.`;
                            } else if (baseMealName.includes('شكشوكة')) {
                                instructions = `لتحضير ${name}:
1. شوّح البصل والفلفل في زيت الزيتون.
2. أضف الطماطم والبهارات واتركها تتسبك.
3. اكسر البيض فوق الخليط.
4. غطّي المقلاة واتركها حتى ينضج البيض.`;
                            } else if (baseMealName.includes('ساندويتشات الجبن الساخن')) {
                                instructions = `لتحضير ${name}:
1. ادهن الخبز بالزبدة من الخارج.
2. ضع الجبن بين شريحتي الخبز.
3. اشوي الساندويتش في مقلاة حتى يذوب الجبن.
4. قدمه ساخناً مقطعاً إلى نصفين.`;
                            } else if (baseMealName.includes('فطائر باللحمة')) {
                                instructions = `لتحضير ${name}:
1. حضّر عجينة الفطائر.
2. حضّر حشوة اللحم المفروم مع البصل والبهارات.
3. احشي العجينة وشكّلها.
4. اخبزها في الفرن حتى تصبح ذهبية.`;
                            } else if (baseMealName.includes('خبز بالبيض والجبن')) {
                                instructions = `لتحضير ${name}:
1. اقطع فتحة في وسط الخبز.
2. ضع الخبز في مقلاة مدهونة بالزبدة.
3. اكسر البيضة في الفتحة.
4. أضف الجبنة الشيدر واتركها تذوب.`;
                            } else if (baseMealName.includes('عجة البيض')) {
                                instructions = `لتحضير ${name}:
1. اخفق البيض مع الملح والفلفل.
2. أضف الخضروات المقطعة والبهارات.
3. اسكب الخليط في مقلاة ساخنة.
4. اطهيها على الوجهين حتى تنضج.`;
                            } else if (baseMealName.includes('حواوشي فطار')) {
                                instructions = `لتحضير ${name}:
1. اخلط اللحم المفروم مع البصل والبهارات.
2. احشي الخبز البلدي بالخليط.
3. ادهن الخبز بقليل من الزيت.
4. اخبزه في الفرن أو على الشواية.`;
                            } else if (baseMealName.includes('بيض بالنقانق')) {
                                instructions = `لتحضير ${name}:
1. اقلي النقانق حتى تنضج.
2. اقلي البطاطس المقطعة حتى تصبح ذهبية.
3. أضف البيض إلى المقلاة.
4. قلّب الخليط حتى ينضج البيض.`;
                            } else if (baseMealName.includes('مربى وقشطة وعسل')) {
                                instructions = `لتحضير ${name}:
1. حمّص الخبز حتى يصبح ذهبياً.
2. ادهن طبقة من القشطة.
3. أضف المربى والعسل.
4. قدمه مع كوب من الشاي أو القهوة.`;
                            } else {
                                instructions = `لتحضير ${name}:
1. حضّر جميع المكونات المطلوبة.
2. اتبع طريقة الطهي التقليدية للوجبة.
3. تبّل حسب الذوق.
4. قدمها ساخنة مع الخبز أو الأرز.`;
                            }
                        } else if (type === 'lunch') {
                            if (baseMealName.includes('محاشي مشكلة')) {
                                instructions = `لتحضير ${name}:
1. حضّر خليط الأرز مع اللحم المفروم والبهارات.
2. احشي الكوسة والباذنجان والفلفل.
3. رتبها في إناء عميق مع الماء والطماطم.
4. اتركها تطبخ على نار هادئة لمدة ساعة.`;
                            } else if (baseMealName.includes('أرز بالخضار وقطع الدجاج')) {
                                instructions = `لتحضير ${name}:
1. تبّل قطع الدجاج واقليها حتى تنضج.
2. في نفس المقلاة، أضف الأرز والخضار.
3. أضف الماء أو المرق واتركه ينضج.
4. قدمه ساخناً مع السلطة.`;
                            } else if (baseMealName.includes('كشري مصري')) {
                                instructions = `لتحضير ${name}:
1. اسلق الأرز والعدس والمكرونة منفصلين.
2. حضّر صلصة الطماطم الحارة.
3. حضّر الدقة (ثوم مقلي مع كزبرة).
4. رتب الطبقات في الطبق وأضف الصلصة والدقة.`;
                            } else if (baseMealName.includes('فتة شاورما')) {
                                instructions = `لتحضير ${name}:
1. حضّر الدجاج الشاورما المتبل.
2. حمّص الخبز وقطّعه.
3. حضّر الثومية والصلصات.
4. رتب الطبقات: خبز، دجاج، ثومية، وصلصة.`;
                            } else if (baseMealName.includes('كفتة بالطحينة')) {
                                instructions = `لتحضير ${name}:
1. شكّل اللحم المفروم على هيئة كفتة.
2. اشويها أو اقليها حتى تنضج.
3. حضّر صوص الطحينة بالليمون والثوم.
4. قدم الكفتة مع الصوص والأرز.`;
                            } else if (baseMealName.includes('طواجن لحمة بالبطاطس')) {
                                instructions = `لتحضير ${name}:
1. قطّع اللحم والبطاطس إلى قطع متوسطة.
2. تبّل اللحم واقليه حتى يحمر.
3. أضف البطاطس والطماطم والماء.
4. اتركه ينضج في الفرن لمدة ساعة.`;
                            } else if (baseMealName.includes('مبكبكة باللحم')) {
                                instructions = `لتحضير ${name}:
1. اسلق المكرونة حتى تنضج.
2. حضّر اللحم المتبل مع البصل.
3. اخلط المكرونة مع اللحم.
4. ضعها في صينية واخبزها في الفرن.`;
                            } else if (baseMealName.includes('أرز معمر باللحم')) {
                                instructions = `لتحضير ${name}:
1. اسلق اللحم مع البهارات حتى ينضج.
2. أضف الأرز إلى مرق اللحم.
3. أضف الزبدة والبهارات.
4. اتركه ينضج على نار هادئة.`;
                            } else if (baseMealName.includes('صيادية السمك')) {
                                instructions = `لتحضير ${name}:
1. نظّف السمك وتبّله بالبهارات.
2. اقلي السمك حتى ينضج.
3. في نفس الزيت، حضّر الأرز بالبصل.
4. قدم السمك فوق الأرز مع الصوص البني.`;
                            } else {
                                instructions = `لتحضير ${name}:
1. حضّر جميع المكونات وتبّلها جيداً.
2. اطبخ المكونات الأساسية حسب النوع.
3. أضف الصلصات والبهارات.
4. قدمها ساخنة مع الأرز أو الخبز.`;
                            }
                        } else if (type === 'dinner') {
                            if (baseMealName.includes('بطاطس محمرة وبرجر')) {
                                instructions = `لتحضير ${name}:
1. قطّع البطاطس واقليها حتى تصبح ذهبية.
2. شكّل اللحم المفروم على هيئة برجر.
3. اشوي البرجر وأضف الجبنة.
4. قدمه مع البطاطس المحمرة.`;
                            } else if (baseMealName.includes('كشري حلة واحدة')) {
                                instructions = `لتحضير ${name}:
1. في حلة واحدة، ضع الأرز والعدس والمكرونة.
2. أضف الماء والبهارات.
3. اتركه ينضج على نار متوسطة.
4. قدمه مع صلصة الطماطم والدقة.`;
                            } else if (baseMealName.includes('فطير مشلتت')) {
                                instructions = `لتحضير ${name}:
1. حضّر عجينة الفطير المشلتت.
2. افردها ورقّقها جيداً.
3. ادهنها بالسمن واطويها.
4. اخبزها وقدمها مع العسل الأسود والطحينة.`;
                            } else if (baseMealName.includes('صينية دجاج بالبطاطس')) {
                                instructions = `لتحضير ${name}:
1. قطّع الدجاج والبطاطس والجزر.
2. تبّل الدجاج بالبهارات وزيت الزيتون.
3. رتب المكونات في صينية فرن.
4. اخبزها لمدة 45 دقيقة حتى تنضج.`;
                            } else if (baseMealName.includes('شوربة سي فود')) {
                                instructions = `لتحضير ${name}:
1. نظّف المأكولات البحرية جيداً.
2. شوّح البصل والثوم في الزبدة.
3. أضف المأكولات البحرية والمرق.
4. أضف الكريمة واتركها تنضج.`;
                            } else if (baseMealName.includes('معكرونة بالصلصة الحمراء')) {
                                instructions = `لتحضير ${name}:
1. اسلق المعكرونة حتى تنضج.
2. حضّر اللحم المفروم مع البصل.
3. أضف صلصة الطماطم والبهارات.
4. اخلط المعكرونة مع الصلصة وقدمها.`;
                            } else if (baseMealName.includes('بيتزا منزلية')) {
                                instructions = `لتحضير ${name}:
1. حضّر عجينة البيتزا أو استخدم الجاهزة.
2. ادهنها بصلصة الطماطم.
3. أضف الخضروات والجبن.
4. اخبزها في الفرن حتى تصبح ذهبية.`;
                            } else if (baseMealName.includes('شيش طاووق')) {
                                instructions = `لتحضير ${name}:
1. قطّع الدجاج إلى مكعبات وتبّله.
2. اتركه منقوعاً لساعات في التتبيلة.
3. اشويه على الفحم مع التقليب.
4. قدمه مع الأرز المبهر والسلطة.`;
                            } else if (baseMealName.includes('ساندويتشات كبدة')) {
                                instructions = `لتحضير ${name}:
1. قطّع الكبدة إلى شرائح وتبّلها.
2. اقليها في الزيت الساخن.
3. حضّر السلطة والطحينة.
4. احشي الخبز الفينو بالكبدة والسلطة.`;
                            } else {
                                instructions = `لتحضير ${name}:
1. حضّر المكونات الأساسية للوجبة.
2. اطبخها حسب الطريقة التقليدية.
3. أضف البهارات والصلصات المناسبة.
4. قدمها ساخنة مع الخبز أو الأرز.`;
                            }
                        } else {
                            instructions = `لتحضير ${name}:
1. حضّر جميع المكونات المطلوبة.
2. اتبع طريقة الطهي المناسبة للوجبة.
3. تبّل حسب الذوق المطلوب.
4. قدمها ساخنة أو باردة حسب نوع الوجبة.`;
                        }
                    }
                }

                const totalCalories = Math.round((caloriesPer100g / 100) * servingSizeGrams);
                const calorieString = `إجمالي السعرات الحرارية: ${totalCalories} سعرة حرارية (لكل 100 جرام: ${caloriesPer100g} سعرة حرارية، حجم الوجبة: ${servingSizeGrams} جرام)`;

                meals.push({ id: `${baseMealName.replace(/\s/g, '')}_${i}`, name, details, ingredients, instructions, calories: calorieString });
            });
            return meals;
        };

        const dietMeals = {
            breakfast: generateMeals(dietBreakfastNames, true, 'breakfast'),
            lunch: generateMeals(dietLunchNames, true, 'lunch'),
            dinner: generateMeals(dietDinnerNames, true, 'dinner'),
        };

        const normalMeals = {
            breakfast: generateMeals(normalBreakfastNames, false, 'breakfast'),
            lunch: generateMeals(normalLunchNames, false, 'lunch'),
            dinner: generateMeals(normalDinnerNames, false, 'dinner'),
        };
        // --- End Data Generation ---

        // All main logic and event listeners are now defined inside window.onload
        window.onload = () => {
            // Helper function to get a random meal
            const getRandomMeal = (mealType, dietType) => {
                const meals = dietType === 'diet' ? dietMeals : normalMeals;
                const categoryMeals = meals[mealType];
                const randomIndex = Math.floor(Math.random() * categoryMeals.length);
                return categoryMeals[randomIndex];
            };

            // Global state variables
            let currentDietType = '';
            let currentSuggestedMealObjects = { breakfast: null, lunch: null, dinner: null };
            let currentMealBeingViewed = null;

            // DOM Elements
            const homePage = document.getElementById('homePage');
            const categoryPage = document.getElementById('categoryPage');
            const mealDetailPage = document.getElementById('mealDetailPage');
            const aboutAppPage = document.getElementById('aboutAppPage');
            const aiModal = document.getElementById('aiModal');
            const aiPromptInput = document.getElementById('aiPromptInput');
            const sendPromptButton = document.getElementById('sendPromptButton');
            const aiLoadingIndicator = document.getElementById('aiLoadingIndicator');
            const aiResponseArea = document.getElementById('aiResponseArea');
            const aiErrorDisplay = document.getElementById('aiErrorDisplay');
            const dietButton = document.getElementById('dietButton');
            const normalButton = document.getElementById('normalButton');
            const aboutAppButton = document.getElementById('aboutAppButton');
            const backToHomeButton = document.getElementById('backToHomeButton');
            const backToCategoryButton = document.getElementById('backToCategoryButton');
            const backFromAboutButton = document.getElementById('backFromAboutButton');
            const categoryTitle = document.getElementById('categoryTitle');
            const suggestMealButtons = document.querySelectorAll('.suggest-meal-button');
            const suggestedMealCards = {
                breakfast: document.getElementById('suggestedBreakfastMeal'),
                lunch: document.getElementById('suggestedLunchMeal'),
                dinner: document.getElementById('suggestedDinnerMeal')
            };
            const viewDetailsButtons = document.querySelectorAll('.view-details-button');
            const mealDetailTitle = document.getElementById('mealDetailTitle');
            const mealDetails = document.getElementById('mealDetails');
            const mealIngredients = document.getElementById('mealIngredients');
            const mealInstructions = document.getElementById('mealInstructions');
            const mealCalories = document.getElementById('mealCalories');
            const allSuggestionNotes = {
                breakfast: document.getElementById('breakfastSuggestionNote'),
                lunch: document.getElementById('lunchSuggestionNote'),
                dinner: document.getElementById('dinnerSuggestionNote')
            };

            // Navigation Functions
            const showPage = (pageId) => {
                [homePage, categoryPage, mealDetailPage, aboutAppPage].forEach(p => p.classList.remove('active'));
                document.getElementById(pageId).classList.add('active');
                window.scrollTo(0, 0);
            };

            const navigateToCategory = (dietType) => {
                currentDietType = dietType;
                categoryTitle.textContent = dietType === 'diet' ? 'وجبات الدايت' : 'وجبات عادية';
                currentSuggestedMealObjects = { breakfast: null, lunch: null, dinner: null };
                document.querySelectorAll('.suggested-meal-card').forEach(card => {
                    card.classList.add('hidden');
                    card.style.display = 'none';
                });
                Object.values(allSuggestionNotes).forEach(note => {
                    if (note) {
                        note.classList.remove('hidden');
                        note.style.display = 'block';
                    }
                });
                showPage('categoryPage');
            };

            const navigateToMealDetail = (meal) => {
                currentMealBeingViewed = meal;
                mealDetailTitle.textContent = meal.name;
                mealDetails.textContent = meal.details;
                mealInstructions.textContent = meal.instructions;
                mealCalories.textContent = meal.calories;
                mealIngredients.innerHTML = '';
                meal.ingredients.forEach((item, index) => {
                    const li = document.createElement('li');
                    li.className = 'flex items-center space-x-3 rtl:space-x-reverse p-3 glass rounded-lg hover:scale-105 transition-all duration-300';
                    li.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                            ${index + 1}
                        </div>
                        <span class="text-white/90 text-lg flex-grow text-right">${item}</span>
                        <i class="fas fa-check text-green-400"></i>
                    `;
                    mealIngredients.appendChild(li);
                });
                showPage('mealDetailPage');
            };

            const toggleAiModal = (show) => {
                if (show) {
                    aiModal.classList.add('active');
                    aiModal.classList.remove('hidden');
                    aiPromptInput.value = '';
                    aiResponseArea.innerHTML = `
                        <div class="text-center text-white/60">
                            <i class="fas fa-comment-dots text-3xl mb-3"></i>
                            <p>ستظهر استجابة الذكاء الاصطناعي هنا</p>
                        </div>
                    `;
                    aiErrorDisplay.classList.add('hidden');
                } else {
                    aiModal.classList.remove('active');
                    setTimeout(() => aiModal.classList.add('hidden'), 300);
                }
            };

            // Gemini API Call Function
            const callGeminiAPI = async (prompt) => {
                aiLoadingIndicator.classList.remove('hidden');
                aiResponseArea.innerHTML = '';
                aiErrorDisplay.classList.add('hidden');
                const apiKey = "AIzaSyAWPuMiQp8ltD3_1v4scm6U-T522GgKWbo";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(`Gemini API Error: ${response.status} - ${errorData.error.message || 'Unknown error'}`);
                    }
                    const result = await response.json();
                    if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts) {
                        const responseText = result.candidates[0].content.parts[0].text;
                        aiResponseArea.innerHTML = `
                            <div class="text-white/90 leading-relaxed whitespace-pre-line">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-robot text-purple-400 text-xl mr-3"></i>
                                    <span class="font-bold text-lg">استجابة الذكاء الاصطناعي:</span>
                                </div>
                                ${responseText.replace(/\n/g, '<br>')}
                            </div>
                        `;
                    } else {
                        throw new Error("Gemini API did not return expected content.");
                    }
                } catch (error) {
                    aiResponseArea.innerHTML = `
                        <div class="text-center text-red-400">
                            <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                            <p>عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.</p>
                        </div>
                    `;
                    aiErrorDisplay.textContent = `خطأ: ${error.message}`;
                    aiErrorDisplay.classList.remove('hidden');
                } finally {
                    aiLoadingIndicator.classList.add('hidden');
                }
            };

            // Attach Event Listeners
            dietButton.addEventListener('click', () => navigateToCategory('diet'));
            normalButton.addEventListener('click', () => navigateToCategory('normal'));
            aboutAppButton.addEventListener('click', () => showPage('aboutAppPage'));
            backToHomeButton.addEventListener('click', () => showPage('homePage'));
            backToCategoryButton.addEventListener('click', () => navigateToCategory(currentDietType));
            backFromAboutButton.addEventListener('click', () => showPage('homePage'));

            suggestMealButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    console.log('Button clicked!', event.target);
                    const mealCategory = event.target.dataset.mealCategory || event.target.closest('[data-meal-category]')?.dataset.mealCategory;
                    console.log('Meal category:', mealCategory);
                    console.log('Current diet type:', currentDietType);

                    if (!mealCategory || !currentDietType) {
                        console.error('Missing meal category or diet type');
                        return;
                    }

                    const meal = getRandomMeal(mealCategory, currentDietType);
                    console.log('Generated meal:', meal);

                    currentSuggestedMealObjects[mealCategory] = meal;
                    const cardElement = suggestedMealCards[mealCategory];
                    const suggestionNoteElement = allSuggestionNotes[mealCategory];

                    if (cardElement && meal) {
                        cardElement.querySelector('.meal-name').textContent = meal.name;
                        cardElement.classList.remove('hidden');
                        cardElement.style.display = 'flex';
                        if (suggestionNoteElement) {
                            suggestionNoteElement.classList.remove('hidden');
                            suggestionNoteElement.style.display = 'block';
                        }
                        console.log('Meal displayed successfully');
                    } else {
                        console.error('Card element or meal not found', { cardElement, meal });
                    }
                });
            });

            viewDetailsButtons.forEach(button => {
                button.addEventListener('click', (event) => {
                    const mealCategory = event.target.dataset.mealCategory;
                    const mealToDisplay = currentSuggestedMealObjects[mealCategory];
                    if (mealToDisplay) {
                        navigateToMealDetail(mealToDisplay);
                    } else {
                        const fallbackMeal = getRandomMeal(mealCategory, currentDietType);
                        navigateToMealDetail(fallbackMeal);
                    }
                });
            });

            aiModifyRecipeButton.addEventListener('click', () => {
                if (currentMealBeingViewed) {
                    toggleAiModal(true);
                } else {
                    alert("يرجى اختيار وجبة أولاً لعرض تفاصيلها.");
                }
            });

            closeModalButton.addEventListener('click', () => toggleAiModal(false));

            // Add event listener for back button in AI modal
            const backFromAiModalButton = document.getElementById('backFromAiModalButton');
            backFromAiModalButton.addEventListener('click', () => toggleAiModal(false));

            // Add event listener to close modal when clicking outside
            aiModal.addEventListener('click', (event) => {
                if (event.target === aiModal) {
                    toggleAiModal(false);
                }
            });

            // Prevent modal from closing when clicking inside the modal content
            const modalContent = aiModal.querySelector('.modal-content');
            modalContent.addEventListener('click', (event) => {
                event.stopPropagation();
            });

            // Add keyboard event listener for Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape' && aiModal.classList.contains('active')) {
                    toggleAiModal(false);
                }
            });

            sendPromptButton.addEventListener('click', () => {
                const userPrompt = aiPromptInput.value.trim();
                if (!userPrompt) {
                    aiErrorDisplay.textContent = "الرجاء إدخال طلبك.";
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }
                if (!currentMealBeingViewed) {
                    aiResponseArea.innerHTML = `
                        <div class="text-center text-red-400">
                            <i class="fas fa-exclamation-triangle text-3xl mb-3"></i>
                            <p>عذراً، لا توجد وجبة حالية لتعديلها.</p>
                        </div>
                    `;
                    aiErrorDisplay.classList.remove('hidden');
                    return;
                }
                const mealDetailsForAI = `الاسم: ${currentMealBeingViewed.name}\nالتفاصيل: ${currentMealBeingViewed.details}\nالمكونات: ${currentMealBeingViewed.ingredients.join(', ')}\nطريقة التحضير: ${currentMealBeingViewed.instructions}\nالسعرات الحرارية: ${currentMealBeingViewed.calories}`;
                const fullPrompt = `أرغب في تعديل وصفة الطعام التالية:\n${mealDetailsForAI}\n\nطلب التعديل: ${userPrompt}\n\nيرجى إعادة كتابة الوصفة بعد التعديل، مع ذكر المكونات وطريقة التحضير والسعرات الحرارية المعدلة (تقديرية). قدم النتيجة بتنسيق واضح وسهل القراءة.`;
                callGeminiAPI(fullPrompt);
            });

            // Initial page load
            showPage('homePage');
        };
    </script>
</body>
</html>
